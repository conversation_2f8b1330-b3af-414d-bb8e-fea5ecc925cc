import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/foundation/containers/ia_rounded_container.dart';
import 'package:ivent_app/core/widgets/composite/buttons/hobby_buttons.dart';
import 'package:ivent_app/shared/domain/entities/hobby.dart';

/// Hobby category selection widget for page creation
///
/// A collapsible widget that displays hobbies grouped by category.
/// Users can select/deselect hobbies and expand/collapse the category
/// to see more options. Integrates with the app's hobby selection system.
class HobbyCategoryBox extends StatefulWidget {
  /// Callback function called when a hobby is toggled
  /// [hobbyId] is the unique identifier of the toggled hobby
  final void Function(String hobbyId) onHobbyToggle;

  /// List of currently selected hobby IDs
  final List<String> selectedHobbyIds;

  /// Name of the main category (e.g., "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>por")
  final String mainCategory;

  /// List of hobbies belonging to this category
  final List<Hobby> hobbyList;

  /// Creates a hobby category selection box
  ///
  /// [onHobbyToggle] is called when a hobby is selected/deselected
  /// [selectedHobbyIds] contains the currently selected hobby IDs
  /// [mainCategory] is the display name for this category
  /// [hobbyList] contains all hobbies in this category
  const HobbyCategoryBox({
    super.key,
    required this.onHobbyToggle,
    required this.selectedHobbyIds,
    required this.mainCategory,
    required this.hobbyList,
  });

  @override
  State<HobbyCategoryBox> createState() => _HobbyCategoryBoxState();
}

class _HobbyCategoryBoxState extends State<HobbyCategoryBox> {
  // ==========================================================================
  // STATE VARIABLES
  // ==========================================================================

  /// Whether the category is currently expanded to show all hobbies
  bool _isExpanded = false;

  // ==========================================================================
  // GETTERS FOR WIDGET PROPERTIES
  // ==========================================================================

  /// Callback function for hobby toggle events
  void Function(String hobbyId) get onHobbyToggle => widget.onHobbyToggle;

  /// Currently selected hobby IDs
  List<String> get selectedHobbyIds => widget.selectedHobbyIds;

  /// Main category name
  String get mainCategory => widget.mainCategory;

  /// List of hobbies in this category
  List<Hobby> get hobbyList => widget.hobbyList;

  // ==========================================================================
  // EVENT HANDLERS
  // ==========================================================================

  /// Toggles the expanded state of the category
  ///
  /// When expanded, all hobbies in the category are shown.
  /// When collapsed, only the first few hobbies are displayed.
  void _toggleExpandedCategory() => setState(() => _isExpanded = !_isExpanded);

  // ==========================================================================
  // BUILD METHODS
  // ==========================================================================

  @override
  Widget build(BuildContext context) {
    return IaRoundedContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Category title
          _buildCategoryTitle(),
          const SizedBox(height: AppDimensions.padding12),

          // Hobby tags
          _buildHobbyTags(),

          // Expand/collapse button (if needed)
          if (_shouldShowExpandButton()) _buildExpandButton(),
        ],
      ),
    );
  }

  /// Builds the category title
  Widget _buildCategoryTitle() {
    return Text(
      mainCategory,
      style: AppTextStyles.size24Bold,
    );
  }

  /// Builds the hobby tags wrap widget
  ///
  /// Shows either all hobbies (when expanded) or a limited number
  /// based on the expansion state.
  Widget _buildHobbyTags() {
    const int maxInitialDisplay = 6; // Show first 6 hobbies initially
    final int displayCount = _isExpanded ? hobbyList.length : maxInitialDisplay;

    return Wrap(
      crossAxisAlignment: WrapCrossAlignment.start,
      spacing: AppDimensions.padding8,
      runSpacing: AppDimensions.padding8,
      children: hobbyList
          .take(displayCount)
          .map((hobby) => HobbyButtons.hobbyTag(
                onTap: () => onHobbyToggle(hobby.hobbyId),
                text: hobby.hobbyName,
                isSelected: selectedHobbyIds.contains(hobby.hobbyId),
              ))
          .toList(),
    );
  }

  /// Builds the expand/collapse button
  Widget _buildExpandButton() {
    return HobbyButtons.expandCategory(
      margin: const EdgeInsets.only(top: AppDimensions.padding12),
      onPressed: _toggleExpandedCategory,
      isExpanded: _isExpanded,
    );
  }

  /// Determines whether the expand button should be shown
  ///
  /// Returns true if there are more hobbies than the initial display limit.
  bool _shouldShowExpandButton() {
    return hobbyList.length > 6;
  }
} 