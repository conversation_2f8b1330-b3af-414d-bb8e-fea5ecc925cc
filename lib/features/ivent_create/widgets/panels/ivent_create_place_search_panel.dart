import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/composite/tiles/ia_list_tile.dart';
import 'package:ivent_app/core/widgets/foundation/graphics/ia_svg_icon.dart';
import 'package:ivent_app/core/widgets/foundation/indicators/ia_loading_indicator.dart';
import 'package:ivent_app/core/widgets/layout/screens/ia_search_screen.dart';
import 'package:ivent_app/features/ivent_create/controllers/ivent_create_controller.dart';
import 'package:ivent_app/features/ivent_create/controllers/sub_controllers/ivent_create_map_controller.dart';
import 'package:ivent_app/shared/controllers/base_search_bar_controller.dart';

class IventCreatePlaceSearchPanel extends StatelessWidget {
  final IventCreateController controller;

  const IventCreatePlaceSearchPanel({
    super.key,
    required this.controller,
  });

  BaseSearchBarController get _baseSearchBarController => controller.mapController.baseSearchBarController;
  IventCreateMapController get _mapController => controller.mapController;

  @override
  Widget build(BuildContext context) {
    return IaSearchScreen(
      margin: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
      searchBarMargin: const EdgeInsets.only(bottom: AppDimensions.padding20),
      textEditingController: _baseSearchBarController.textEditingController,
      searchBarLabelText: 'Haritada Mekan Ara',
      body: _buildSearchResults(),
    );
  }

  Widget _buildSearchResults() {
    return Obx(() {
      if (_baseSearchBarController.isSearching) {
        return _buildLoadingState();
      }
      if (_mapController.searchSuggestions.isEmpty) {
        return _buildEmptyState();
      }

      return _buildSuggestionsList();
    });
  }

  Widget _buildLoadingState() {
    return const Center(
      child: IaLoadingIndicator(),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const IaSvgIcon(
            iconPath: AppAssets.mapPin,
            iconSize: 50,
            iconColor: AppColors.textTertiary,
          ),
          const SizedBox(height: AppDimensions.padding16),
          Text(
            'Konum aramak için yazmaya başlayın',
            style: AppTextStyles.size16RegularTextSecondary,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildSuggestionsList() {
    return ListView.separated(
      padding: const EdgeInsets.only(bottom: 100),
      itemCount: _mapController.searchSuggestions.length,
      itemBuilder: _buildSuggestionItem,
      separatorBuilder: IaListTile.separatorBuilder20,
    );
  }

  Widget _buildSuggestionItem(BuildContext context, int index) {
    final suggestion = _mapController.searchSuggestions[index];
    return IaListTile.withSvgIcon(
      iconPath: AppAssets.mapPin,
      iconColor: AppColors.primary,
      title: suggestion.name,
      subtitle: suggestion.fullAddress ?? suggestion.placeFormatted,
      onTap: () => _handleSuggestionTap(suggestion),
    );
  }

  void _handleSuggestionTap(suggestion) {
    _mapController.selectPlace(suggestion);
  }
}
