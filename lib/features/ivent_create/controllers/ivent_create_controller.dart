import 'package:get/get.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/ivent_create/controllers/ivent_create_state_manager.dart';
import 'package:ivent_app/features/ivent_create/controllers/sub_controllers/ivent_create_form_controller.dart';
import 'package:ivent_app/features/ivent_create/controllers/sub_controllers/ivent_create_image_controller.dart';
import 'package:ivent_app/features/ivent_create/controllers/sub_controllers/ivent_create_map_controller.dart';
import 'package:ivent_app/features/ivent_create/controllers/sub_controllers/ivent_create_submission_controller.dart';
import 'package:ivent_app/routes/ivent_create.dart';
import 'package:ivent_app/shared/controllers/base_controller.dart';

/// Main controller for iVent creation feature
///
/// Orchestrates all child controllers and manages navigation between
/// different steps of the iVent creation process. This controller
/// serves as the main entry point and coordinator for all iVent creation
/// functionality.
class IventCreateController extends BaseController<IventCreateSharedState> {
  // Child controllers
  late final IventCreateMapController mapController;
  late final IventCreateFormController formController;
  late final IventCreateImageController imageController;
  late final IventCreateSubmissionController submissionController;

  // State
  final _isInitializationComplete = false.obs;

  // Constructor
  IventCreateController(
    AuthService authService,
    IventCreateSharedState state,
  ) : super(authService, state);

  // Getters
  bool get isInitializationComplete => _isInitializationComplete.value;

  // Setters
  set isInitializationComplete(bool value) => _isInitializationComplete.value = value;

  // Lifecycle methods

  @override
  Future<void> initController() async {
    super.initController();
    await _initializeChildControllers();
    isInitializationComplete = true;
    print('IventCreateController has been initialized with user: ${sessionUser.sessionId}');
  }

  @override
  void closeController() {
    _disposeChildControllers();
    super.closeController();
  }

  // Navigation methods

  /// Navigates to main category selection page
  void goToMainCategoryPage() {
    try {
      Get.toNamed(IventCreateRoutes.IVENT_OLUSTUR_KATEGORI_SECINIZ);
    } catch (e) {
      handleError(e);
    }
  }

  /// Navigates to subcategory selection page
  void goToSubCategoryPage() {
    try {
      Get.toNamed(IventCreateRoutes.IVENT_OLUSTUR_ALT_KATEGORI_SECINIZ);
    } catch (e) {
      handleError(e);
    }
  }

  /// Navigates to image selection page
  void goToImageSelectionPage() {
    try {
      imageController.getSuggestedImagesPage();
    } catch (e) {
      handleError(e);
    }
  }

  /// Navigates to image gallery page
  void goToImageGalleryPage() {
    try {
      Get.toNamed(IventCreateRoutes.IVENT_OLUSTUR_IMAGE_GALLERY);
    } catch (e) {
      handleError(e);
    }
  }

  /// Navigates to date selection page
  void goToDateSelectionPage() {
    try {
      Get.toNamed(IventCreateRoutes.IVENT_OLUSTUR_DATE_SELECTION);
    } catch (e) {
      handleError(e);
    }
  }

  /// Navigates to map page
  void goToMapPage() {
    try {
      Get.toNamed(IventCreateRoutes.IVENT_OLUSTUR_MAP);
    } catch (e) {
      handleError(e);
    }
  }

  /// Navigates to preview page
  void goToPreviewPage() {
    try {
      Get.toNamed(IventCreateRoutes.IVENT_OLUSTUR_PREVIEW);
    } catch (e) {
      handleError(e);
    }
  }

  /// Navigates to description page
  void goToDescriptionPage() {
    try {
      Get.toNamed(IventCreateRoutes.IVENT_OLUSTUR_ACIKLAMA);
    } catch (e) {
      handleError(e);
    }
  }

  /// Navigates to tags page
  void goToTagsPage() {
    try {
      Get.toNamed(IventCreateRoutes.IVENT_OLUSTUR_TAGS);
    } catch (e) {
      handleError(e);
    }
  }

  /// Navigates to registration type page
  void goToRegisterTypePage() {
    try {
      Get.toNamed(IventCreateRoutes.IVENT_OLUSTUR_KAYIT_TURU_SECINIZ);
    } catch (e) {
      handleError(e);
    }
  }

  // Private methods

  /// Initializes all child controllers
  Future<void> _initializeChildControllers() async {
    mapController = Get.put(IventCreateMapController(authService, state));
    formController = Get.put(IventCreateFormController(authService, state));
    imageController = Get.put(IventCreateImageController(authService, state));
    submissionController = Get.put(IventCreateSubmissionController(authService, state));
  }

  /// Disposes all child controllers
  void _disposeChildControllers() {
    Get.delete<IventCreateMapController>();
    Get.delete<IventCreateFormController>();
    Get.delete<IventCreateImageController>();
    Get.delete<IventCreateSubmissionController>();
  }
}
