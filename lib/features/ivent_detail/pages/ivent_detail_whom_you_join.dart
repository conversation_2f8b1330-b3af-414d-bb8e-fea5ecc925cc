import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/widgets/composite/tiles/ia_list_tile.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_floating_action_button.dart';
import 'package:ivent_app/core/widgets/foundation/graphics/ia_svg_icon.dart';
import 'package:ivent_app/core/widgets/layout/scaffolds/ia_scaffold.dart';
import 'package:ivent_app/features/ivent_detail/controllers/ivent_details_controller.dart';
import 'package:ivent_app/features/ivent_detail/widgets/lists/selection_list.dart';

class IventDetailWhomYouJoin extends StatelessWidget {
  final TextEditingController _searchBarController = TextEditingController();
  final String iventId;

  IventDetailWhomYouJoin(this.iventId, {Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final IventDetailsController _controller = Get.find(tag: iventId);
    final IventViewTypeEnum viewType = _controller.iventInfoController.iventPage!.viewType;

    return Obx(() {
      final invitableGroups = _controller.invitationsController.invitableGroups;
      final invitableUsers = _controller.invitationsController.invitableUsers;
      if (invitableGroups == null || invitableUsers == null) {
        return const Center(child: CircularProgressIndicator());
      }
      return IaScaffold.search(
        title: 'Kimlerle Katılıyorsun?',
        textEditingController: _searchBarController,
        body: SingleChildScrollView(
          child: Column(
            children: [
              _buildSingleParticipationTile(_controller),
              const SizedBox(height: AppDimensions.padding20),
              if (invitableGroups.groupCount != 0) groupSelectionList(invitableGroups, _controller),
              if (invitableUsers.friendCount != 0) userSelectionList(invitableUsers, _controller),
              const SizedBox(height: 100),
            ],
          ),
        ),
        floatingActionButton: IaFloatingActionButton(
          onPressed: () => _controller.invitationsController.joinIventAndInviteFriends(context),
          isEnabled: _controller.invitationsController.selectedFriendCount != 0,
          text: _controller.invitationsController.selectedFriendCount != 0
              ? '${_controller.invitationsController.selectedFriendCount} ' +
                  (viewType == IventViewTypeEnum.default_ ? 'Arkadaşınla Katıl' : 'Kişiyi Çağır')
              : 'Birden Fazla Seçebilirsin',
        ),
      );
    });
  }

  Widget _buildSingleParticipationTile(IventDetailsController controller) {
    return IaListTile.withImageUrl(
      avatarUrl: controller.sessionUser.sessionAvatarUrl,
      title: '${controller.sessionUser.sessionFullname} (Sen)',
      subtitle: 'Tek Katılıyorum',
      onTap: () => controller.invitationsController.joinIvent(),
      trailing: const IaSvgIcon(iconPath: AppAssets.chevronRight, iconColor: AppColors.darkGrey),
    );
  }
}
