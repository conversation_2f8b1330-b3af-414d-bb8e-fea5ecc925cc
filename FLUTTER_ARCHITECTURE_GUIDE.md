# Flutter Architecture & Coding Standards Guide

## Overview

This guide defines the architectural patterns and coding standards for the iVent Flutter application based on analysis of the Auth and Home features. The architecture follows a feature-based modular approach with clean separation of concerns.

## Architecture Principles

### 1. Feature-Based Architecture

- **Structure**: Each feature is organized under `lib/features/{feature_name}/`
- **Isolation**: Features are self-contained with minimal cross-feature dependencies
- **Modularity**: Each feature can be developed, tested, and maintained independently

### 2. Layer Separation

- **Presentation Layer**: Pages and Widgets
- **Business Logic Layer**: Controllers and State Management
- **Data Layer**: Models and API Integration
- **Shared Layer**: Common utilities, base classes, and shared components

## Directory Structure Standards

### Feature Directory Organization

```
lib/features/{feature_name}/
├── constants/           # Feature-specific constants
├── controllers/         # Business logic controllers
│   └── sub_controllers/ # Specialized controllers
├── models/             # Data models and DTOs
├── pages/              # UI screens/pages
├── utils/              # Feature-specific utilities (optional)
└── widgets/            # Feature-specific widgets
    ├── common/         # Shared widgets within feature
    ├── form/           # Form-related widgets
    └── {category}/     # Categorized widgets
```

### Core Directory Structure

```
lib/core/
├── cache/              # Caching mechanisms
├── constants/          # App-wide constants
├── error/              # Error handling
├── services/           # Core services (auth, etc.)
├── utils/              # Utility functions
└── widgets/            # Reusable UI components
    ├── composite/      # Complex composed widgets
    ├── foundation/     # Basic UI elements
    ├── layout/         # Layout components
    └── specialized/    # Domain-specific widgets
```

## Naming Conventions

### File Naming

- **Snake Case**: All file names use snake_case
- **Descriptive**: Names clearly indicate purpose
- **Suffixes**: Use appropriate suffixes for clarity
  - `_controller.dart` for controllers
  - `_page.dart` for pages
  - `_widget.dart` for widgets
  - `_model.dart` for models
  - `_constants.dart` for constants

### Class Naming

- **PascalCase**: All class names use PascalCase
- **Descriptive**: Names clearly indicate functionality
- **Suffixes**: Use consistent suffixes
  - `Controller` for business logic controllers
  - `Page` for screen/page widgets
  - `Widget` for custom widgets
  - `State` for state management classes
  - `Item` for model classes

### Variable and Method Naming

- **camelCase**: All variables and methods use camelCase
- **Private Members**: Prefix with underscore `_`
- **Boolean Variables**: Use `is`, `has`, `can` prefixes
- **Getters/Setters**: Use descriptive names without `get`/`set` prefixes

## Controller Architecture

### Base Controller Pattern

All controllers must extend `BaseController<T extends SharedState>`:

```dart
class FeatureController extends BaseController<FeatureSharedState> {
  FeatureController(AuthService authService, FeatureSharedState state)
    : super(authService, state);

  @override
  Future<void> initController() async {
    super.initController();
    // Initialize child controllers and resources
  }

  @override
  void closeController() {
    // Clean up resources and child controllers
    super.closeController();
  }
}
```

### Controller Hierarchy

1. **Main Controller**: Coordinates feature functionality
2. **Sub-Controllers**: Handle specific responsibilities
3. **Shared State**: Manages reactive state across controllers

### Sub-Controller Management

- Initialize sub-controllers in dependency order
- Use `Get.put()` for registration
- Clean up with `Get.delete()` in `closeController()`
- Pass shared dependencies (authService, state) to sub-controllers

## State Management

### Shared State Pattern

Each feature has a dedicated shared state class:

```dart
class FeatureSharedState extends SharedState {
  // Reactive variables using GetX observables
  final _property = ''.obs;
  final _list = <Type>[].obs;
  final _nullable = Rxn<Type>();

  // Getters and setters
  Type get property => _property.value;
  set property(Type value) => _property.value = value;

  // Helper methods for state manipulation
  void clearAll() {
    // Reset state to defaults
  }
}
```

### State Management Rules

- Use GetX observables (`.obs`, `Rxn<T>()`) for reactive state
- Provide getters and setters for clean access
- Include helper methods for complex state operations
- Keep state classes focused on data, not business logic

## Loading and Error Handling

### Loading Management

Use `runWithLoading()` for async operations:

```dart
Future<void> performAction() async {
  await runWithLoading(
    () async {
      // Async operation
    },
    loadingTag: 'actionName',
    errorMessage: 'Custom error message',
    onError: () => {/* error handling */},
    onSuccess: () => {/* success handling */},
  );
}
```

### Error Handling

- Use `BaseController.handleError()` for consistent error display
- Provide custom error messages when appropriate
- Handle errors gracefully without crashing the app

## Page Structure

### Page Organization

```dart
class FeaturePage extends StatefulWidget {
  const FeaturePage({super.key});

  @override
  State<FeaturePage> createState() => _FeaturePageState();
}

class _FeaturePageState extends State<FeaturePage> {
  late final FeatureController _controller;

  @override
  void initState() {
    super.initState();
    _controller = Get.find<FeatureController>();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // UI implementation
    );
  }
}
```

### Page Rules

- Use `StatefulWidget` for pages with local state
- Get controllers using `Get.find<T>()`
- Initialize controllers in `initState()`
- Use `Obx()` for reactive UI updates
- Dispose resources properly

## Widget Architecture

### Widget Categories

1. **Common Widgets**: Shared within a feature
2. **Form Widgets**: Input and form-related components
3. **Specialized Widgets**: Feature-specific UI components

### Widget Structure

```dart
class CustomWidget extends StatelessWidget {
  final RequiredType requiredParam;
  final OptionalType? optionalParam;
  final VoidCallback? onAction;

  const CustomWidget({
    super.key,
    required this.requiredParam,
    this.optionalParam,
    this.onAction,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      // Widget implementation
    );
  }
}
```

## Constants Organization

### Constants Structure

```dart
class FeatureConstants {
  FeatureConstants._(); // Private constructor

  // Group constants by category
  static const String constantName = 'value';
  static const int numericConstant = 42;

  // Use descriptive comments for complex constants
  /// Maximum length for user input validation
  static const int maxInputLength = 50;
}
```

### Constants Rules

- Use private constructor to prevent instantiation
- Group related constants together
- Use descriptive names and comments
- Separate by functional areas (validation, UI, strings)

## Model Classes

### Model Structure

```dart
class ModelItem {
  final String requiredField;
  final String? optionalField;

  ModelItem({
    required this.requiredField,
    this.optionalField,
  });

  factory ModelItem.fromApiModel(ApiModel apiModel) {
    return ModelItem(
      requiredField: apiModel.field,
      optionalField: apiModel.optionalField,
    );
  }

  @override
  String toString() {
    return 'ModelItem(requiredField: $requiredField, optionalField: $optionalField)';
  }
}
```

### Model Rules

- Use immutable classes with final fields
- Provide factory constructors for API conversion
- Include `toString()` method for debugging
- Use descriptive field names

## Import Organization

### Import Order

1. Flutter/Dart imports
2. Third-party package imports
3. App-wide imports (core, shared)
4. Feature-specific imports
5. Relative imports

### Import Example

```dart
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/shared/controllers/base_controller.dart';
import 'package:ivent_app/features/auth/models/contact.dart';
import '../constants/auth_strings.dart';
```

## Navigation Patterns

### Route Definition

```dart
abstract class FeatureRoutes {
  FeatureRoutes._();

  static const FEATURE_PAGE = '/feature_page';
  static const DETAIL_PAGE = '/detail_page';
}
```

### Navigation Methods

- Use descriptive method names: `goToFeaturePage()`
- Handle navigation in controllers, not widgets
- Pass parameters through route arguments when needed

## Documentation Standards

### Class Documentation

```dart
/// Brief description of the class purpose
///
/// Detailed explanation of functionality, usage patterns,
/// and any important implementation details.
class ExampleClass {
  /// Brief description of the method
  ///
  /// [parameter] Description of parameter usage
  /// Returns description of return value
  void exampleMethod(String parameter) {
    // Implementation
  }
}
```

### Documentation Rules

- Use triple-slash comments for public APIs
- Provide brief and detailed descriptions
- Document parameters and return values
- Include usage examples for complex functionality

## Code Quality Standards

### General Rules

1. **Single Responsibility**: Each class has one clear purpose
2. **Dependency Injection**: Use constructor injection for dependencies
3. **Immutability**: Prefer immutable classes and final fields
4. **Error Handling**: Handle errors gracefully with user feedback
5. **Performance**: Use lazy loading and efficient state management
6. **Testing**: Write testable code with clear separation of concerns

### Code Review Checklist

- [ ] Follows naming conventions
- [ ] Proper directory structure
- [ ] Extends appropriate base classes
- [ ] Handles errors gracefully
- [ ] Uses reactive state management correctly
- [ ] Includes proper documentation
- [ ] Follows import organization
- [ ] Implements proper resource cleanup

## API Integration Patterns

### Service Layer Integration

Controllers access APIs through the `AuthService`:

```dart
class FeatureController extends BaseController<FeatureSharedState> {
  Future<void> fetchData() async {
    await runWithLoading(
      () async {
        final response = await authService.featureApi.getData();
        if (response != null) {
          // Process response
        }
      },
      loadingTag: 'fetchData',
    );
  }
}
```

### API Response Handling

- Always check for null responses
- Use `runWithLoading()` for API calls
- Handle errors through the base controller
- Transform API models to domain models

## Validation Patterns

### Form Validation

```dart
class ValidationController extends BaseController<FeatureSharedState> {
  bool get isFormValid => _validateAllFields();

  bool _validateAllFields() {
    return _validateField1() && _validateField2();
  }

  bool _validateField1() {
    return state.field1.length >= ValidationConstants.minLength;
  }
}
```

### Validation Rules

- Create validation constants for reusable rules
- Implement validation in controllers, not widgets
- Provide real-time validation feedback
- Use descriptive validation error messages

## Utility Functions

### Feature-Specific Utils

Place utility functions in `lib/features/{feature}/utils/`:

```dart
/// Removes duplicate items from a list based on ID
List<T> removeDuplicateItems<T extends HasId>(
  List<T> items, {
  List<T> exclude = const [],
}) {
  // Implementation
}
```

### Utility Rules

- Create pure functions without side effects
- Use generic types when applicable
- Provide comprehensive documentation
- Include parameter validation

## Dependency Injection Patterns

### Route Bindings

```dart
class FeatureBindings implements Bindings {
  @override
  void dependencies() {
    if (Get.isRegistered<FeatureController>()) return;

    Get.lazyPut(() => FeatureSharedState(), fenix: true);
    Get.lazyPut<FeatureController>(
      () => FeatureController(
        Get.find<AuthService>(),
        Get.find<FeatureSharedState>(),
      ),
      fenix: true,
    );
  }
}
```

### Binding Rules

- Use `lazyPut()` for lazy initialization
- Set `fenix: true` for persistent instances
- Check registration to avoid duplicates
- Inject dependencies through constructor

## Search Functionality Patterns

### Search Controller Extension

For features with search functionality:

```dart
class SearchController extends BaseControllerWithSearch<FeatureSharedState> {
  final _searchResults = <ResultType>[].obs;

  @override
  bool get isResultsEmpty => _searchResults.isEmpty;

  @override
  Future<void> onSearch([String? query]) async {
    await runWithLoading(
      () async {
        final results = await authService.api.search(query);
        _searchResults.assignAll(results ?? []);
      },
      loadingTag: 'search',
    );
  }
}
```

## Performance Optimization

### Lazy Loading

- Use `Get.lazyPut()` for controller registration
- Implement pagination for large data sets
- Load data on-demand in `initController()`

### Memory Management

- Dispose controllers in `closeController()`
- Clear large collections when not needed
- Use weak references for temporary data

### UI Performance

- Use `Obx()` for minimal reactive rebuilds
- Avoid unnecessary widget rebuilds
- Implement efficient list rendering

## Testing Considerations

### Testable Architecture

- Inject dependencies through constructors
- Separate business logic from UI
- Use pure functions for utilities
- Mock external dependencies

### Test Structure

```dart
void main() {
  group('FeatureController', () {
    late FeatureController controller;
    late MockAuthService mockAuthService;
    late FeatureSharedState state;

    setUp(() {
      mockAuthService = MockAuthService();
      state = FeatureSharedState();
      controller = FeatureController(mockAuthService, state);
    });

    test('should perform expected behavior', () {
      // Test implementation
    });
  });
}
```

## Security Considerations

### Data Protection

- Validate all user inputs
- Sanitize data before API calls
- Handle sensitive data appropriately
- Use secure storage for credentials

### Error Information

- Don't expose sensitive information in errors
- Log errors securely for debugging
- Provide user-friendly error messages
- Handle authentication failures gracefully

## Localization Patterns

### String Constants

Organize strings by feature and category:

```dart
class FeatureStrings {
  FeatureStrings._();

  // Button texts
  static const String continueButton = 'Devam Et';
  static const String cancelButton = 'İptal';

  // Error messages
  static const String validationError = 'Girdiğiniz bilgi geçersiz';

  // Informational messages
  static const String welcomeMessage = 'Hoş geldiniz';
}
```

## Migration and Versioning

### Feature Evolution

- Maintain backward compatibility when possible
- Use feature flags for gradual rollouts
- Document breaking changes clearly
- Provide migration guides for major changes

### API Versioning

- Handle API version changes gracefully
- Implement fallback mechanisms
- Test with multiple API versions
- Document API dependencies

This comprehensive guide ensures consistent, maintainable, and scalable Flutter code across all features in the iVent application, promoting best practices and architectural excellence.
