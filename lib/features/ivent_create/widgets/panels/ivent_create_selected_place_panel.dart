import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_floating_action_button.dart';
import 'package:ivent_app/core/widgets/specialized/ivent/ivent_create_buttons.dart';
import 'package:ivent_app/core/widgets/composite/tiles/ia_list_tile.dart';
import 'package:ivent_app/features/ivent_create/controllers/ivent_create_controller.dart';
import 'package:ivent_app/features/mapbox/models/ia_location_item.dart';

/// A panel widget that displays the selected place information during ivent creation.
///
/// This widget shows the details of the place that has been selected from the
/// search results, including the place name and address. It provides options
/// to either clear the selection or proceed to the preview page.
///
/// The panel includes:
/// - Place information display with icon
/// - Clear selection button
/// - Action button to proceed to preview
///
/// This widget is typically shown after a user selects a place from the
/// search suggestions in the map interface.
class IventCreateSelectedPlacePanel extends StatelessWidget {
  /// The ivent creation controller containing the selected place data
  final IventCreateController controller;

  const IventCreateSelectedPlacePanel({
    super.key,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    // Ensure we have a selected place before building
    if (controller.state.selectedPlace == null) {
      return const SizedBox.shrink();
    }

    final selectedPlace = controller.state.selectedPlace!;

    return Column(
      children: [
        _buildPlaceInfo(selectedPlace),
        _buildActionButton(),
      ],
    );
  }

  /// Builds the place information display
  Widget _buildPlaceInfo(IaLocationItem selectedPlace) {
    return IaListTile.withSvgIcon(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      iconPath: AppAssets.mapPin,
      iconColor: AppColors.primary,
      title: selectedPlace.name,
      subtitle: selectedPlace.address,
      trailing: _buildClearButton(),
    );
  }

  /// Builds the clear selection button
  Widget _buildClearButton() {
    return IventCreateButtons.closeButtonDark(
      onTap: _handleClearSelection,
    );
  }

  /// Builds the action button to proceed to preview
  Widget _buildActionButton() {
    return IaFloatingActionButton(
      text: 'Konumu Seç ve Etkinliği Önizle',
      onPressed: _handleProceedToPreview,
      isEnabled: true,
      isPrimary: false,
    );
  }

  /// Handles clearing the selected place
  void _handleClearSelection() {
    controller.mapController.clearSearch();
  }

  /// Handles proceeding to the preview page
  void _handleProceedToPreview() {
    controller.goToPreviewPage();
  }
}
