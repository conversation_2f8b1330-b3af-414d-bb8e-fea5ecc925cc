import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/index.dart';
import 'package:ivent_app/features/settings/controllers/settings_controller.dart';
import 'package:ivent_app/features/settings/widgets/settings_list_tile.dart';

class AboutPage extends StatelessWidget {
  const AboutPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<SettingsController>();
    
    return IaScaffold.noSearch(
      title: 'Hakkında',
      body: ListView(
        padding: const EdgeInsets.all(AppDimensions.padding20),
        children: [
          // Uygulama Bilgileri
          _buildSectionHeader('Uygulama'),
          Container(
            padding: const EdgeInsets.all(AppDimensions.padding16),
            decoration: BoxDecoration(
              color: AppColors.white,
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              border: Border.all(color: AppColors.grey200, width: 1),
            ),
            child: Column(
              children: [
                Image.asset(
                  AppAssets.iventLogo,
                  height: 60,
                  width: 60,
                ),
                const SizedBox(height: AppDimensions.padding12),
                Text(
                  'iVent',
                  style: AppTextStyles.size16Bold,
                ),
                const SizedBox(height: AppDimensions.padding4),
                Text(
                  'Sürüm 1.0.0',
                  style: AppTextStyles.size12Regular.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
                const SizedBox(height: AppDimensions.padding8),
                Text(
                  'Hepimizin şehrin içinde şehirden uzak hissettiği anlar olmuştur. '
                  'Henüz varlığını bile bilmediğin nice tecrübeler çok yakınında. '
                  'Ve iVent App\'de bunları keşfedeceksin!',
                  style: AppTextStyles.size12Regular.copyWith(
                    color: AppColors.textSecondary,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
          const SizedBox(height: AppDimensions.padding20),
          
          // Yasal Belgeler
          _buildSectionHeader('Yasal Belgeler'),
          SettingsListTile(
            icon: AppAssets.bookOpen,
            title: 'Gizlilik Politikası',
            subtitle: 'Verilerinizin nasıl korunduğu',
            onTap: controller.openPrivacyPolicy,
          ),
          const SizedBox(height: AppDimensions.padding12),
          SettingsListTile(
            icon: AppAssets.bookOpen,
            title: 'Kullanım Şartları',
            subtitle: 'Uygulama kullanım koşulları',
            onTap: controller.openTermsOfService,
          ),
          const SizedBox(height: AppDimensions.padding20),
          
          // Topluluk Kuralları
          _buildSectionHeader('Topluluk Kuralları'),
          SettingsListTile(
            icon: AppAssets.users,
            title: 'Topluluk Kuralları',
            subtitle: 'Güvenli ve saygılı bir ortam için',
            onTap: () => _showCommunityRulesDialog(),
          ),
          const SizedBox(height: AppDimensions.padding20),
          
          // İletişim
          _buildSectionHeader('İletişim'),
          SettingsListTile(
            icon: AppAssets.mail,
            title: 'Web Sitesi',
            subtitle: 'ivent.app',
            onTap: controller.openWebsite,
          ),
          const SizedBox(height: AppDimensions.padding20),
          
          // Telif Hakkı
          Container(
            padding: const EdgeInsets.all(AppDimensions.padding16),
            decoration: BoxDecoration(
              color: AppColors.grey100,
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
            ),
            child: Column(
              children: [
                Text(
                  '© 2025 iVent App',
                  style: AppTextStyles.size12Bold.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
                const SizedBox(height: AppDimensions.padding4),
                Text(
                  'Tüm hakları saklıdır.',
                  style: AppTextStyles.size10Regular.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(
        bottom: AppDimensions.padding12,
        top: AppDimensions.padding16,
      ),
      child: Text(
        title,
        style: AppTextStyles.size14BoldPrimary,
      ),
    );
  }

  void _showCommunityRulesDialog() {
    Get.dialog(
      AlertDialog(
        backgroundColor: AppColors.background,
        title: Text(
          'Topluluk Kuralları',
          style: AppTextStyles.size16Bold,
        ),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'iVent topluluğunda güvenli ve saygılı bir ortam için:',
                style: AppTextStyles.size14Regular,
              ),
              const SizedBox(height: AppDimensions.padding16),
              _buildRuleItem('1. Saygılı Olun', 
                'Diğer kullanıcılara karşı saygılı davranın. Nefret söylemi, taciz veya tehdit yasaktır.'),
              _buildRuleItem('2. Gerçek Bilgiler', 
                'Sahte bilgiler paylaşmayın. Etkinlik bilgileri doğru ve güncel olmalıdır.'),
              _buildRuleItem('3. Uygun İçerik', 
                'Müstehcen, şiddet içeren veya uygunsuz içerik paylaşmayın.'),
              _buildRuleItem('4. Telif Hakları', 
                'Başkalarına ait içerikleri izinsiz paylaşmayın.'),
              _buildRuleItem('5. Spam Yasak', 
                'Spam içerik veya tekrarlayan mesajlar göndermeyin.'),
              _buildRuleItem('6. Kişisel Bilgiler', 
                'Kendi veya başkalarının kişisel bilgilerini paylaşmayın.'),
              const SizedBox(height: AppDimensions.padding16),
              Text(
                'Bu kurallara uymayan kullanıcılar uyarı alabilir veya hesapları kapatılabilir.',
                style: AppTextStyles.size12Regular.copyWith(
                  color: AppColors.warning,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
          ),
        ),
        actions: [
          ElevatedButton(
            onPressed: () => Get.back(),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: AppColors.white,
            ),
            child: Text(
              'Anladım',
              style: AppTextStyles.size14Bold.copyWith(color: AppColors.white),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRuleItem(String title, String description) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppDimensions.padding12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: AppTextStyles.size14Bold.copyWith(color: AppColors.primary),
          ),
          const SizedBox(height: AppDimensions.padding4),
          Text(
            description,
            style: AppTextStyles.size12Regular.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }
} 