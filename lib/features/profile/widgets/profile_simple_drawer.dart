import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/core/widgets/index.dart';
import 'package:ivent_app/features/profile/controllers/profile_state_manager.dart';
import 'package:ivent_app/features/profile/controllers/sub_controllers/profile_side_menu_controller.dart';

class ProfileSimpleDrawer extends StatelessWidget {
  const ProfileSimpleDrawer({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Controller'ı lazy olarak oluştur
    final controller =
        Get.put(ProfileSideMenuController(Get.find<AuthService>(), Get.find<ProfileSharedState>()), tag: 'sideMenu');
    final user = controller.sessionUser;

    return Drawer(
      backgroundColor: AppColors.background,
      child: Safe<PERSON><PERSON>(
        child: Column(
          children: [
            // Header - Kullanıcı Bilgileri
            Container(
              padding: const EdgeInsets.all(AppDimensions.padding20),
              child: Column(
                children: [
                  // Avatar ve İsim
                  Obx(() => CircleAvatar(
                        radius: 40,
                        backgroundImage: controller.userBanner?.avatarUrl != null
                            ? NetworkImage(controller.userBanner!.avatarUrl!)
                            : (user.sessionAvatarUrl != null ? NetworkImage(user.sessionAvatarUrl!) : null),
                        backgroundColor: AppColors.lightGrey,
                        child: controller.userBanner?.avatarUrl == null && user.sessionAvatarUrl == null
                            ? Text(
                                user.sessionFullname.isNotEmpty
                                    ? user.sessionFullname.substring(0, 1).toUpperCase()
                                    : 'U',
                                style: AppTextStyles.size24Bold,
                              )
                            : null,
                      )),
                  const SizedBox(height: AppDimensions.padding12),
                  Text(
                    '@${user.sessionUsername}',
                    style: AppTextStyles.size16Bold,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: AppDimensions.padding4),
                  InkWell(
                    onTap: controller.onAccountManagementTap,
                    child: Text(
                      'Hesap Yönetimi',
                      style: AppTextStyles.size14Regular.copyWith(
                        color: AppColors.primary,
                        decoration: TextDecoration.underline,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ),
            ),

            // Level Bilgisi - Tam Hesap
            GestureDetector(
              onTap: controller.onLevelInfoTap,
              child: Container(
                margin: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
                padding: const EdgeInsets.all(AppDimensions.padding16),
                decoration: BoxDecoration(
                  color: AppColors.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                  border: Border.all(color: AppColors.primary.withOpacity(0.3)),
                ),
                child: Row(
                  children: [
                    Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: AppColors.primary,
                        borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                      ),
                      child: const Icon(
                        Icons.check_circle,
                        color: AppColors.white,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: AppDimensions.padding12),
                    Expanded(
                      child: Obx(() => Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                controller.levelText,
                                style: AppTextStyles.size14Bold,
                              ),
                              const SizedBox(height: AppDimensions.padding4),
                              Text(
                                controller.levelDescription,
                                style: AppTextStyles.size12Regular.copyWith(color: AppColors.textSecondary),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ],
                          )),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: AppDimensions.padding16),

            // edu.tr Onayla
            if (controller.showEduVerification)
              Container(
                margin: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
                child: _buildActionButton(
                  icon: Icons.email,
                  title: 'edu.tr Onayla',
                  subtitle: 'Üniversite Etkinliklerini Keşfetmek İçin',
                  onTap: controller.onEduVerificationTap,
                  backgroundColor: AppColors.darkGrey,
                ),
              ),

            if (controller.showEduVerification) const SizedBox(height: AppDimensions.padding12),

            // iVent Creator Olmak
            if (controller.showCreatorRequest)
              Container(
                margin: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
                child: _buildActionButton(
                  icon: Icons.star,
                  title: 'iVent Creator Olmak',
                  subtitle: 'Eşsiz Deneyimler ve Çok Daha Fazlası...',
                  onTap: controller.onCreatorRequestTap,
                  backgroundColor: const Color(0xFFFF8C00), // Orange color
                ),
              ),

            const SizedBox(height: AppDimensions.padding20),

            // Sayfalarım Kısmı
            Obx(() {
              final pages = controller.userPages;

              if (pages == null) {
                return Container(
                  margin: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
                  child: Text(
                    'Sayfalar yükleniyor...',
                    style: AppTextStyles.size14Regular.copyWith(color: AppColors.textSecondary),
                  ),
                );
              }

              return Container(
                margin: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Sayfalarım',
                      style: AppTextStyles.size16Bold.copyWith(color: AppColors.primary),
                    ),
                    const SizedBox(height: AppDimensions.padding12),

                    // Sayfa Oluştur Butonu
                    _buildCreatePageItem(
                      onTap: () => controller.onCreatePageTap(),
                    ),

                    // Mevcut Sayfalar
                    if (pages.pages.isNotEmpty) ...[
                      const SizedBox(height: AppDimensions.padding8),
                      ...pages.pages
                          .map((page) => _buildPageItem(
                                pageId: page.pageId,
                                pageName: page.pageName,
                                pageImageUrl: page.thumbnailUrl,
                                membershipStatus: page.pageMembershipStatus.toString().split('.').last,
                                onTap: () => controller.onPageTap(page.pageId, page.pageName),
                              ))
                          .toList(),
                    ],
                  ],
                ),
              );
            }),

            // Spacer
            const Spacer(),

            // Alt Kısım - Çıkış Yap ve Hesap Silme
            Container(
              padding: const EdgeInsets.all(AppDimensions.padding20),
              decoration: const BoxDecoration(
                border: Border(
                  top: BorderSide(color: AppColors.lightGrey, width: 1),
                ),
              ),
              child: Column(
                children: [
                  // Hesap Silme Butonu
                  _buildDrawerItem(
                    icon: AppAssets.closeCircle,
                    title: 'Hesabı Sil',
                    titleColor: AppColors.error,
                    iconColor: AppColors.error,
                    onTap: () {
                      Get.back();
                      _showDeleteAccountDialog(context);
                    },
                  ),

                  const SizedBox(height: AppDimensions.padding8),

                  // Çıkış Yap Butonu
                  _buildDrawerItem(
                    icon: AppAssets.closeLG,
                    title: 'Çıkış Yap',
                    titleColor: AppColors.error,
                    iconColor: AppColors.error,
                    onTap: () {
                      Get.back();
                      _showLogoutDialog(context);
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDrawerItem({
    required String icon,
    required String title,
    required VoidCallback onTap,
    Color? titleColor,
    Color? iconColor,
  }) {
    return ListTile(
      leading: IaSvgIcon(
        iconPath: icon,
        iconSize: 24,
        iconColor: iconColor ?? AppColors.darkGrey,
      ),
      title: Text(
        title,
        style: AppTextStyles.size14Medium.copyWith(
          color: titleColor ?? AppColors.textPrimary,
        ),
      ),
      onTap: onTap,
      contentPadding: const EdgeInsets.symmetric(
        horizontal: AppDimensions.padding20,
        vertical: AppDimensions.padding4,
      ),
    );
  }

  Widget _buildActionButton({
    required dynamic icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    required Color backgroundColor,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        ),
        child: Padding(
          padding: const EdgeInsets.all(AppDimensions.padding16),
          child: Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: AppColors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                ),
                child: icon is IconData
                    ? Icon(icon, color: AppColors.white, size: 24)
                    : IaSvgIcon(
                        iconPath: icon,
                        iconSize: 24,
                        iconColor: AppColors.white,
                      ),
              ),
              const SizedBox(width: AppDimensions.padding12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      title,
                      style: AppTextStyles.size14Bold.copyWith(color: AppColors.white),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: AppDimensions.padding4),
                    Text(
                      subtitle,
                      style: AppTextStyles.size12Regular.copyWith(
                        color: AppColors.white.withOpacity(0.8),
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCreatePageItem({
    required VoidCallback onTap,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppDimensions.padding8),
      child: ListTile(
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: AppColors.primary.withOpacity(0.1),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(color: AppColors.primary, width: 2),
          ),
          child: const Icon(
            Icons.add,
            color: AppColors.primary,
            size: 24,
          ),
        ),
        title: Text(
          'Sayfa Oluştur',
          style: AppTextStyles.size14Medium.copyWith(color: AppColors.primary),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        subtitle: Text(
          'Mekan veya Topluluklar İçin',
          style: AppTextStyles.size12Regular.copyWith(color: AppColors.textSecondary),
        ),
        onTap: onTap,
        dense: true,
        contentPadding: const EdgeInsets.symmetric(
          horizontal: AppDimensions.padding16,
          vertical: AppDimensions.padding4,
        ),
      ),
    );
  }

  Widget _buildPageItem({
    required String pageId,
    required String pageName,
    required String? pageImageUrl,
    required String membershipStatus,
    required VoidCallback onTap,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppDimensions.padding8),
      child: ListTile(
        leading: CircleAvatar(
          radius: 20,
          backgroundImage: pageImageUrl != null ? NetworkImage(pageImageUrl) : null,
          backgroundColor: AppColors.lightGrey,
          child: pageImageUrl == null
              ? Text(
                  pageName.substring(0, 1).toUpperCase(),
                  style: AppTextStyles.size14Bold,
                )
              : null,
        ),
        title: Text(
          pageName,
          style: AppTextStyles.size14Medium,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        subtitle: Text(
          membershipStatus == 'admin'
              ? 'Yardımcı Admin'
              : membershipStatus == 'creator'
                  ? 'Sayfa Admini'
                  : membershipStatus == 'moderator'
                      ? 'Sayfa Admini'
                      : 'Üye',
          style: AppTextStyles.size12Regular.copyWith(color: AppColors.textSecondary),
        ),
        onTap: onTap,
        dense: true,
        contentPadding: const EdgeInsets.symmetric(
          horizontal: AppDimensions.padding16,
          vertical: AppDimensions.padding4,
        ),
      ),
    );
  }

  void _showLogoutDialog(BuildContext context) {
    Get.dialog(
      AlertDialog(
        backgroundColor: AppColors.background,
        title: Text('Çıkış Yap', style: AppTextStyles.size16Bold),
        content: Text(
          'Çıkış yapmak istediğinizden emin misiniz?',
          style: AppTextStyles.size14Regular,
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text('İptal', style: AppTextStyles.size14Medium),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              Get.find<AuthService>().logoutWithContext(context);
            },
            child: Text(
              'Çıkış Yap',
              style: AppTextStyles.size14Bold.copyWith(color: AppColors.error),
            ),
          ),
        ],
      ),
    );
  }

  void _showDeleteAccountDialog(BuildContext context) {
    Get.dialog(
      AlertDialog(
        backgroundColor: AppColors.background,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusL),
        ),
        insetPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
        title: Row(
          children: [
            const IaSvgIcon(
              iconPath: AppAssets.circleWarning,
              iconSize: 24,
              iconColor: AppColors.error,
            ),
            const SizedBox(width: AppDimensions.padding12),
            Text(
              'Hesabı Sil',
              style: AppTextStyles.size16Bold.copyWith(color: AppColors.error),
            ),
          ],
        ),
        content: ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 300),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Hesabınızı silmek istediğinizden emin misiniz?',
                style: AppTextStyles.size14Bold,
              ),
              const SizedBox(height: AppDimensions.padding8),
              Text(
                'Aşağıdaki verileriniz kalıcı olarak silinecektir:',
                style: AppTextStyles.size14Regular.copyWith(color: AppColors.textSecondary),
              ),
              const SizedBox(height: AppDimensions.padding12),
              _buildDeleteWarningItem('• Profil bilgileriniz'),
              _buildDeleteWarningItem('• Oluşturduğunuz iVent\'ler'),
              _buildDeleteWarningItem('• Arkadaşlık bağlantılarınız'),
              _buildDeleteWarningItem('• Memories\'leriniz'),
              _buildDeleteWarningItem('• Favorileriniz'),
              const SizedBox(height: AppDimensions.padding16),
              Container(
                padding: const EdgeInsets.all(AppDimensions.padding12),
                decoration: BoxDecoration(
                  color: AppColors.error.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                  border: Border.all(color: AppColors.error.withOpacity(0.3)),
                ),
                child: Row(
                  children: [
                    const IaSvgIcon(
                      iconPath: AppAssets.circleWarning,
                      iconSize: 20,
                      iconColor: AppColors.error,
                    ),
                    const SizedBox(width: AppDimensions.padding8),
                    Expanded(
                      child: Text(
                        'Bu işlem geri alınamaz!',
                        style: AppTextStyles.size12Bold.copyWith(color: AppColors.error),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text(
              'İptal',
              style: AppTextStyles.size14Medium.copyWith(color: AppColors.textSecondary),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              Get.find<AuthService>().deleteAccountWithContext(context);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: AppColors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              ),
            ),
            child: Text(
              'Hesabı Sil',
              style: AppTextStyles.size14Bold.copyWith(color: AppColors.white),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDeleteWarningItem(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppDimensions.padding4),
      child: Text(
        text,
        style: AppTextStyles.size12Regular.copyWith(color: AppColors.textSecondary),
      ),
    );
  }
}
