import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/widgets/foundation/containers/ia_rounded_container.dart';
import 'package:ivent_app/shared/domain/entities/hobby.dart';

/// A widget that displays an ivent tag with selection functionality.
///
/// This widget represents a hobby/interest tag that can be selected or deselected.
/// It provides visual feedback for the selection state and follows the project's
/// design patterns for tag display.
///
/// The tag displays the hobby name with an icon indicating whether it can be
/// added (plus icon) or removed (close icon) based on the selection state.
///
/// Usage:
/// ```dart
/// IventTag(
///   tag: hobby,
///   isSelected: selectedTags.contains(hobby),
///   onTap: (tag) => toggleTagSelection(tag),
/// )
/// ```
class IventTag extends StatelessWidget {
  /// The hobby/tag data to display
  final Hobby tag;
  
  /// Callback function called when the tag is tapped
  /// Receives the tag as a parameter
  final Function(Hobby tag)? onTap;
  
  /// Whether this tag is currently selected
  final bool isSelected;

  /// Creates an ivent tag widget.
  ///
  /// The [tag] parameter is required and contains the hobby data to display.
  /// The [onTap] callback is optional and will be called when the tag is tapped.
  /// The [isSelected] parameter defaults to false and controls the visual state.
  const IventTag({
    super.key,
    required this.tag,
    this.onTap,
    this.isSelected = false,
  });

  @override
  Widget build(BuildContext context) {
    return IaRoundedContainer(
      onTap: () => onTap?.call(tag),
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
      color: isSelected ? AppColors.primary : AppColors.white,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            tag.hobbyName,
            style: TextStyle(
              color: isSelected ? AppColors.white : AppColors.grey600,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(width: 5),
          Icon(
            isSelected ? Icons.close : Icons.add,
            size: 14,
            color: isSelected ? AppColors.white : AppColors.grey600,
          ),
        ],
      ),
    );
  }
}
