import 'package:flutter/widgets.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/features/profile/controllers/profile_controller.dart';

class ProfileCount extends StatelessWidget {
  final int count;
  final String text;
  final VoidCallback? onTap;

  const ProfileCount._({
    required this.count,
    required this.text,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Text(count.toString(), style: AppTextStyles.size16Bold),
          Text(text, style: AppTextStyles.size12MediumTextSecondary),
        ],
      ),
    );
  }

  static ProfileCount iventCount(ProfileController controller) {
    final pageContext = controller.userInfoController.userPageInfo!;
    return ProfileCount._(
      count: pageContext.iventCount,
      text: "iVent'ler",
      onTap: controller.goToIventsPage,
    );
  }

  static ProfileCount friendCount(ProfileController controller) {
    final pageContext = controller.userInfoController.userPageInfo!;
    return ProfileCount._(
      count: pageContext.friendCount,
      text: 'Arkadaşlar',
      onTap: controller.goToFriendsPage,
    );
  }

  static ProfileCount followerCount(ProfileController controller) {
    final pageContext = controller.userInfoController.userPageInfo!;
    return ProfileCount._(
      count: pageContext.followerCount,
      text: 'Takipçiler',
      onTap: controller.goToFollowersPage,
    );
  }
}
