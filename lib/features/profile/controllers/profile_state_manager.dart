import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/shared/controllers/shared_state.dart';

class ProfileSharedState extends SharedState {
  final String userId;

  ProfileSharedState(this.userId);

  final _userRole = Rx<UserRoleEnum?>(null);

  UserRoleEnum? get userRole => _userRole.value;

  set userRole(UserRoleEnum? value) => _userRole.value = value;
}
