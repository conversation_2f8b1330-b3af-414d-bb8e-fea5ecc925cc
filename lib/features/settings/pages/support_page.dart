import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/index.dart';
import 'package:ivent_app/features/settings/controllers/settings_controller.dart';
import 'package:ivent_app/features/settings/widgets/settings_list_tile.dart';

class SupportPage extends StatelessWidget {
  const SupportPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<SettingsController>();
    
    return IaScaffold.noSearch(
      title: 'Destek',
      body: ListView(
        padding: const EdgeInsets.all(AppDimensions.padding20),
        children: [
          // İletişim
          _buildSectionHeader('İletişim'),
          SettingsListTile(
            icon: AppAssets.mail,
            title: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
            subtitle: 'Bizimle iletişime geçin',
            onTap: controller.openWebsite,
          ),
          const SizedBox(height: AppDimensions.padding20),
          
          // Sıkça Sorulan Sorular
          _buildSectionHeader('Sıkça Sorulan Sorular'),
          SettingsListTile(
            icon: AppAssets.help,
            title: 'Hesap nasıl oluşturulur?',
            subtitle: 'Telefon numaranızla kayıt olun',
            onTap: () => _showFaqDialog('Hesap Oluşturma', 
              'iVent\'e kaydolmak için:\n\n'
              '1. Telefon numaranızı girin\n'
              '2. SMS ile gelen kodu doğrulayın\n'
              '3. Ad-soyad ve ilgi alanlarınızı seçin\n'
              '4. Profil fotoğrafınızı ekleyin'),
          ),
          const SizedBox(height: AppDimensions.padding12),
          SettingsListTile(
            icon: AppAssets.help,
            title: 'iVent nasıl oluşturulur?',
            subtitle: 'Etkinlik oluşturma rehberi',
            onTap: () => _showFaqDialog('iVent Oluşturma', 
              'iVent oluşturmak için:\n\n'
              '1. Ana sayfada + butonuna tıklayın\n'
              '2. Etkinlik detaylarını doldurun\n'
              '3. Konum ve tarih seçin\n'
              '4. Fotoğraf ekleyin\n'
              '5. Yayınlayın'),
          ),
          const SizedBox(height: AppDimensions.padding12),
          SettingsListTile(
            icon: AppAssets.help,
            title: 'Nasıl arkadaş eklerim?',
            subtitle: 'Arkadaş ekleme ve takip etme',
            onTap: () => _showFaqDialog('Arkadaş Ekleme', 
              'Arkadaş eklemek için:\n\n'
              '1. Profil sayfasına gidin\n'
              '2. "Arkadaş Ekle" butonuna tıklayın\n'
              '3. Veya arama yaparak kişileri bulun\n'
              '4. Creator hesapları için "Takip Et" butonunu kullanın'),
          ),
          const SizedBox(height: AppDimensions.padding12),
          SettingsListTile(
            icon: AppAssets.help,
            title: 'Hesap seviyesi nasıl artar?',
            subtitle: 'Level sistemi hakkında',
            onTap: () => _showFaqDialog('Hesap Seviyeleri', 
              'Hesap seviyeleri:\n\n'
              '• Level 0: Profil eksik\n'
              '• Level 1: Profil tamamlandı\n'
              '• Level 2: 5 favoriye ekledi\n'
              '• Level 3: iVent\'e katıldı\n'
              '• Level 4: Memories oluşturdu\n'
              '• Level 5: Grup oluşturdu'),
          ),
          const SizedBox(height: AppDimensions.padding12),
          SettingsListTile(
            icon: AppAssets.help,
            title: 'Creator nasıl olunur?',
            subtitle: 'iVent Creator başvurusu',
            onTap: () => _showFaqDialog('Creator Olmak', 
              'Creator olmak için:\n\n'
              '1. Profil sayfasından "Creator Ol" seçin\n'
              '2. Gerekli seviyeleri tamamlayın\n'
              '3. Başvuru formunu doldurun\n'
              '4. İnceleme sürecini bekleyin\n'
              '5. Onaylandıktan sonra Creator özelliklerini kullanın'),
          ),
          const SizedBox(height: AppDimensions.padding20),
          
          // Bilgilendirme
          Container(
            padding: const EdgeInsets.all(AppDimensions.padding16),
            decoration: BoxDecoration(
              color: AppColors.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              border: Border.all(color: AppColors.primary.withOpacity(0.2)),
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    const IaSvgIcon(
                      iconPath: AppAssets.info,
                      iconColor: AppColors.primary,
                      iconSize: 20,
                    ),
                    const SizedBox(width: AppDimensions.padding8),
                    Text(
                      'Yardım',
                      style: AppTextStyles.size14BoldPrimary,
                    ),
                  ],
                ),
                const SizedBox(height: AppDimensions.padding8),
                Text(
                  'Başka sorularınız varsa "İletişim" bölümünden bizimle iletişime geçebilirsiniz. '
                  'Size en kısa sürede yardımcı olmaya çalışacağız.',
                  style: AppTextStyles.size12Regular.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(
        bottom: AppDimensions.padding12,
        top: AppDimensions.padding16,
      ),
      child: Text(
        title,
        style: AppTextStyles.size14BoldPrimary,
      ),
    );
  }

  void _showFaqDialog(String title, String content) {
    Get.dialog(
      AlertDialog(
        backgroundColor: AppColors.background,
        title: Text(
          title,
          style: AppTextStyles.size16Bold,
        ),
        content: SingleChildScrollView(
          child: Text(
            content,
            style: AppTextStyles.size14Regular,
          ),
        ),
        actions: [
          ElevatedButton(
            onPressed: () => Get.back(),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: AppColors.white,
            ),
            child: Text(
              'Anladım',
              style: AppTextStyles.size14Bold.copyWith(color: AppColors.white),
            ),
          ),
        ],
      ),
    );
  }
} 