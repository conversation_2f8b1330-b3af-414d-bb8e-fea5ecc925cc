import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/index.dart';
import 'package:ivent_app/features/settings/controllers/settings_controller.dart';
import 'package:ivent_app/features/settings/widgets/settings_list_tile.dart';

class SettingsPage extends StatelessWidget {
  const SettingsPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(SettingsController());
    
    return IaScaffold.noSearch(
      title: 'Ayarlar',
      body: ListView(
        padding: const EdgeInsets.all(AppDimensions.padding20),
        children: [
          // <PERSON><PERSON><PERSON><PERSON>ölümü
          _buildSectionHeader('Giz<PERSON>lik'),
          SettingsListTile(
            icon: AppAssets.shield,
            title: 'Gizlilik Ayarları',
            subtitle: 'Hesap gizliliği ve engellenen kişiler',
            onTap: controller.goToPrivacySettings,
          ),
          const SizedBox(height: AppDimensions.padding12),
          
          // Güvenlik Bölümü
          _buildSectionHeader('Güvenlik'),
          SettingsListTile(
            icon: AppAssets.lock,
            title: 'Güvenlik Ayarları',
            subtitle: 'E-posta, telefon ve hesap güvenliği',
            onTap: controller.goToSecuritySettings,
          ),
          const SizedBox(height: AppDimensions.padding12),
          
          // Destek Bölümü
          _buildSectionHeader('Destek'),
          SettingsListTile(
            icon: AppAssets.help,
            title: 'Destek',
            subtitle: 'İletişim ve sıkça sorulan sorular',
            onTap: controller.goToSupportPage,
          ),
          const SizedBox(height: AppDimensions.padding12),
          
          // Hakkında Bölümü
          _buildSectionHeader('Hakkında'),
          SettingsListTile(
            icon: AppAssets.info,
            title: 'Hakkında',
            subtitle: 'Gizlilik politikası ve kullanım şartları',
            onTap: controller.goToAboutPage,
          ),
          const SizedBox(height: AppDimensions.padding32),
          
          // Çıkış ve Hesap Silme
          _buildSectionHeader('Hesap'),
          SettingsListTile(
            icon: AppAssets.logOut,
            title: 'Çıkış Yap',
            subtitle: 'Hesabınızdan çıkış yapın',
            onTap: controller.logout,
            textColor: AppColors.warning,
          ),
          const SizedBox(height: AppDimensions.padding8),
          SettingsListTile(
            icon: AppAssets.deleteColumn,
            title: 'Hesabı Sil',
            subtitle: 'Hesabınızı kalıcı olarak silin',
            onTap: () => controller.deleteAccount(context),
            textColor: AppColors.error,
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(
        bottom: AppDimensions.padding12,
        top: AppDimensions.padding16,
      ),
      child: Text(
        title,
        style: AppTextStyles.size14BoldPrimary,
      ),
    );
  }
} 