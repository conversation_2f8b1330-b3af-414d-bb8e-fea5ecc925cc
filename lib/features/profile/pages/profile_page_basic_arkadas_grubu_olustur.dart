import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/routes/profile.dart';
import 'package:ivent_app/core/widgets/composite/tiles/ia_list_tile.dart';
import 'package:ivent_app/core/widgets/foundation/indicators/ia_loading_indicator.dart';
import 'package:ivent_app/core/widgets/layout/scaffolds/ia_scaffold.dart';
import 'package:ivent_app/features/profile/controllers/profile_controller.dart';

class ProfilePageBasicArkadasGrubuOlustur extends StatelessWidget {
  final ProfileController controller = Get.find();
  final TextEditingController _searchBarController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return IaScaffold.search(
      title: 'Arkadaşlar',
      textEditingController: _searchBarController,
      body: Obx(() {
        final pageContext = controller.socialController.followersContext;
        if (pageContext == null) {
          return const IaLoadingIndicator();
        }
        return ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          padding: const EdgeInsets.only(bottom: 100),
          itemCount: pageContext.followerCount,
          itemBuilder: (context, index) {
            final element = pageContext.followers[index];
            return IaListTile.withImageUrl(
              avatarUrl: element.avatarUrl,
              title: '@${element.username}',
              subtitle: element.university,
              onTap: () => Get.toNamed(ProfileRoutes.PROFILE_PAGE, arguments: element.userId),
            );
          },
        );
      }),
    );
  }
}
