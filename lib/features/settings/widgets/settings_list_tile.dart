import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/foundation/graphics/ia_svg_icon.dart';

class SettingsListTile extends StatelessWidget {
  final String icon;
  final String title;
  final String subtitle;
  final VoidCallback? onTap;
  final Color? textColor;
  final Color? iconColor;
  final Color? titleColor;
  final Widget? trailing;

  const SettingsListTile({
    Key? key,
    required this.icon,
    required this.title,
    required this.subtitle,
    this.onTap,
    this.textColor,
    this.iconColor,
    this.titleColor,
    this.trailing,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        child: Container(
          padding: const EdgeInsets.all(AppDimensions.padding16),
          decoration: BoxDecoration(
            color: AppColors.white,
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
            border: Border.all(color: AppColors.grey200, width: 1),
          ),
          child: Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: (iconColor ?? AppColors.primary).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                ),
                child: Center(
                  child: IaSvgIcon(
                    iconPath: icon,
                    iconColor: iconColor ?? AppColors.primary,
                    iconSize: 20,
                  ),
                ),
              ),
              const SizedBox(width: AppDimensions.padding16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: AppTextStyles.size16Medium.copyWith(
                        color: titleColor ?? textColor ?? AppColors.textPrimary,
                      ),
                    ),
                    const SizedBox(height: AppDimensions.padding4),
                    Text(
                      subtitle,
                      style: AppTextStyles.size14Regular.copyWith(
                        color: textColor ?? AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
              if (trailing != null) ...[
                const SizedBox(width: AppDimensions.padding8),
                trailing!,
              ] else if (onTap != null) ...[
                const SizedBox(width: AppDimensions.padding8),
                const Icon(
                  Icons.chevron_right,
                  color: AppColors.grey400,
                  size: 24,
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
} 