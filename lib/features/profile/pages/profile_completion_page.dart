import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/layout/scaffolds/ia_scaffold.dart';
import 'package:ivent_app/features/profile/controllers/sub_controllers/profile_completion_controller.dart';

class ProfileCompletionPage extends GetView<ProfileCompletionController> {
  const ProfileCompletionPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return IaScaffold.noSearch(
      title: 'Hesa<PERSON>ilgileri',
      body: Obx(() {
        if (controller.isLoading() && controller.currentUsername.isEmpty) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }
        return _buildBody();
      }),
      showBackButton: true,
    );
  }

  Widget _buildBody() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppDimensions.padding16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Profile Image Section
          _buildProfileImageSection(),
          const SizedBox(height: AppDimensions.padding24),

          // Full Name Section (Read-only from session)
          _buildFullNameSection(),
          const SizedBox(height: AppDimensions.padding16),

          // Username Section (Editable)
          _buildUsernameSection(),
          const SizedBox(height: AppDimensions.padding16),

          // Birth Date Section
          _buildBirthDateSection(),
          const SizedBox(height: AppDimensions.padding16),

          // Gender Section
          _buildGenderSection(),
          const SizedBox(height: AppDimensions.padding32),

          // Profile Completion Progress
          _buildProfileCompletionProgress(),
          const SizedBox(height: AppDimensions.padding16),

          // Save Button
          _buildSaveButton(),
        ],
      ),
    );
  }

  Widget _buildProfileImageSection() {
    return Center(
      child: Column(
        children: [
          GestureDetector(
            onTap: controller.pickImage,
            child: Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: AppColors.grey200,
                border: Border.all(
                  color: AppColors.grey300,
                  width: 2,
                ),
              ),
              child: Obx(() {
                if (controller.profileImage.value != null) {
                  return ClipRRect(
                    borderRadius: BorderRadius.circular(60),
                    child: Image.file(
                      controller.profileImage.value!,
                      fit: BoxFit.cover,
                    ),
                  );
                } else if (controller.currentAvatarUrl.value.isNotEmpty) {
                  return ClipRRect(
                    borderRadius: BorderRadius.circular(60),
                    child: Image.network(
                      controller.currentAvatarUrl.value,
                      fit: BoxFit.cover,
                      loadingBuilder: (context, child, loadingProgress) {
                        if (loadingProgress == null) return child;
                        return Center(
                          child: CircularProgressIndicator(
                            value: loadingProgress.expectedTotalBytes != null
                                ? loadingProgress.cumulativeBytesLoaded / loadingProgress.expectedTotalBytes!
                                : null,
                          ),
                        );
                      },
                      errorBuilder: (context, error, stackTrace) {
                        return _buildDefaultAvatar();
                      },
                    ),
                  );
                } else {
                  return _buildDefaultAvatar();
                }
              }),
            ),
          ),
          const SizedBox(height: AppDimensions.padding8),
          GestureDetector(
            onTap: controller.pickImage,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.camera_alt,
                  size: 16,
                  color: AppColors.primary,
                ),
                const SizedBox(width: 4),
                Text(
                  'Fotoğraf Değiştir',
                  style: AppTextStyles.size14Medium.copyWith(
                    color: AppColors.primary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDefaultAvatar() {
    return const Icon(
      Icons.person,
      size: 60,
      color: AppColors.grey500,
    );
  }

  Widget _buildFullNameSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Ad Soyad',
          style: AppTextStyles.size16Medium.copyWith(
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: AppDimensions.padding8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(
            horizontal: AppDimensions.padding16,
            vertical: AppDimensions.padding12,
          ),
          decoration: BoxDecoration(
            color: AppColors.grey50,
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
            border: Border.all(color: AppColors.grey300),
          ),
          child: Obx(() => Text(
                controller.currentFullname.value.isNotEmpty ? controller.currentFullname.value : 'Yükleniyor...',
                style: AppTextStyles.size16Regular.copyWith(
                  color: controller.currentFullname.value.isNotEmpty ? AppColors.textSecondary : AppColors.grey500,
                ),
              )),
        ),
        const SizedBox(height: 4),
        Text(
          'Ad soyad bilginiz değiştirilemez',
          style: AppTextStyles.size12Regular.copyWith(
            color: AppColors.grey500,
          ),
        ),
      ],
    );
  }

  Widget _buildUsernameSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Kullanıcı Adı',
              style: AppTextStyles.size16Medium.copyWith(
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(width: 4),
            Text(
              '*',
              style: AppTextStyles.size16Medium.copyWith(
                color: Colors.red,
              ),
            ),
          ],
        ),
        const SizedBox(height: AppDimensions.padding8),
        Container(
          decoration: BoxDecoration(
            color: AppColors.grey100,
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
            border: Border.all(color: AppColors.grey300),
          ),
          child: TextField(
            controller: controller.usernameController,
            style: AppTextStyles.size16Regular.copyWith(
              color: AppColors.textPrimary,
            ),
            decoration: InputDecoration(
              hintText: 'Kullanıcı adınızı giriniz',
              hintStyle: AppTextStyles.size16Regular.copyWith(
                color: AppColors.textSecondary,
              ),
              border: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: AppDimensions.padding16,
                vertical: AppDimensions.padding12,
              ),
              prefixIcon: const Icon(
                Icons.alternate_email,
                color: AppColors.grey500,
                size: 20,
              ),
            ),
            onChanged: (value) {
              // Trigger reactive updates
              controller.usernameController.text = value;
            },
          ),
        ),
        const SizedBox(height: 4),
        Text(
          'Sadece harf, rakam, alt çizgi ve tire kullanabilirsiniz',
          style: AppTextStyles.size12Regular.copyWith(
            color: AppColors.grey500,
          ),
        ),
      ],
    );
  }

  Widget _buildBirthDateSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Doğum Tarihi',
              style: AppTextStyles.size16Medium.copyWith(
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(width: 4),
            Text(
              '*',
              style: AppTextStyles.size16Medium.copyWith(
                color: Colors.red,
              ),
            ),
          ],
        ),
        const SizedBox(height: AppDimensions.padding8),
        GestureDetector(
          onTap: controller.pickBirthDate,
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(
              horizontal: AppDimensions.padding16,
              vertical: AppDimensions.padding12,
            ),
            decoration: BoxDecoration(
              color: AppColors.grey100,
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              border: Border.all(
                  color: controller.hasSelectedBirthDate ? AppColors.primary.withOpacity(0.3) : AppColors.grey300),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Obx(() => Text(
                      controller.birthDateText.value,
                      style: AppTextStyles.size16Regular.copyWith(
                        color: controller.birthDateText.value == 'GG/AA/YYYY'
                            ? AppColors.textSecondary
                            : AppColors.textPrimary,
                      ),
                    )),
                Icon(
                  Icons.calendar_today,
                  color: controller.hasSelectedBirthDate ? AppColors.primary : AppColors.grey500,
                  size: 20,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildGenderSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Cinsiyet',
              style: AppTextStyles.size16Medium.copyWith(
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(width: 4),
            Text(
              '*',
              style: AppTextStyles.size16Medium.copyWith(
                color: Colors.red,
              ),
            ),
          ],
        ),
        const SizedBox(height: AppDimensions.padding8),
        GestureDetector(
          onTap: controller.showGenderPicker,
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(
              horizontal: AppDimensions.padding16,
              vertical: AppDimensions.padding12,
            ),
            decoration: BoxDecoration(
              color: AppColors.grey100,
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              border: Border.all(
                  color: controller.hasSelectedGender ? AppColors.primary.withOpacity(0.3) : AppColors.grey300),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Obx(() => Text(
                      controller.selectedGender.value.isEmpty ? 'Cinsiyet seçiniz' : controller.selectedGender.value,
                      style: AppTextStyles.size16Regular.copyWith(
                        color:
                            controller.selectedGender.value.isEmpty ? AppColors.textSecondary : AppColors.textPrimary,
                      ),
                    )),
                Icon(
                  Icons.keyboard_arrow_down,
                  color: controller.hasSelectedGender ? AppColors.primary : AppColors.grey500,
                  size: 24,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildProfileCompletionProgress() {
    return Obx(() {
      final completedFields = [
        controller.hasProfileImage,
        controller.usernameController.text.isNotEmpty,
        controller.hasSelectedBirthDate,
        controller.hasSelectedGender,
      ].where((field) => field).length;

      const totalFields = 4;
      final progress = completedFields / totalFields;

      return Container(
        padding: const EdgeInsets.all(AppDimensions.padding16),
        decoration: BoxDecoration(
          color: AppColors.grey50,
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          border: Border.all(color: AppColors.grey200),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Profil Tamamlanma',
                  style: AppTextStyles.size14Medium.copyWith(
                    color: AppColors.textPrimary,
                  ),
                ),
                Text(
                  '${(progress * 100).toInt()}%',
                  style: AppTextStyles.size14Bold.copyWith(
                    color: AppColors.primary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            LinearProgressIndicator(
              value: progress,
              backgroundColor: AppColors.grey200,
              valueColor: const AlwaysStoppedAnimation<Color>(AppColors.primary),
            ),
            const SizedBox(height: 8),
            Text(
              '$completedFields / $totalFields alan tamamlandı',
              style: AppTextStyles.size12Regular.copyWith(
                color: AppColors.grey500,
              ),
            ),
          ],
        ),
      );
    });
  }

  Widget _buildSaveButton() {
    return SizedBox(
      width: double.infinity,
      child: Obx(() => ElevatedButton(
            onPressed: controller.isLoading() ? null : controller.saveProfile,
            style: ElevatedButton.styleFrom(
              backgroundColor: controller.isProfileComplete ? AppColors.primary : AppColors.grey400,
              foregroundColor: AppColors.white,
              padding: const EdgeInsets.symmetric(vertical: AppDimensions.padding16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              ),
              elevation: 0,
            ),
            child: controller.isLoading()
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      color: AppColors.white,
                      strokeWidth: 2,
                    ),
                  )
                : Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      if (controller.isProfileComplete) ...[
                        const Icon(Icons.check_circle, size: 20),
                        const SizedBox(width: 8),
                      ],
                      Text(
                        controller.isProfileComplete ? 'Profili Tamamla' : 'Kaydet',
                        style: AppTextStyles.size16Bold.copyWith(
                          color: AppColors.white,
                        ),
                      ),
                    ],
                  ),
          )),
    );
  }
}
