import 'package:flutter/widgets.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/foundation/graphics/ia_svg_icon.dart';

class ContactTile extends StatelessWidget {
  final String iconPath;
  final String text;

  const ContactTile({
    super.key,
    required this.iconPath,
    required this.text,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        IaSvgIcon(iconPath: iconPath, iconSize: 24, iconColor: AppColors.darkGrey),
        const SizedBox(width: AppDimensions.padding8),
        Expanded(child: Text(text, style: AppTextStyles.size16RegularTextSecondary, maxLines: 1, softWrap: false)),
      ],
    );
  }

  static ContactTile googleForms({
    required String text,
  }) {
    return ContactTile(
      iconPath: AppAssets.googleForms,
      text: text,
    );
  }

  static ContactTile instagram({
    required String text,
  }) {
    return ContactTile(
      iconPath: AppAssets.instagram,
      text: text,
    );
  }

  static ContactTile whatsappGroup({
    required String text,
  }) {
    return ContactTile(
      iconPath: AppAssets.whatsapp,
      text: text,
    );
  }

  static ContactTile whatsappMessage({
    required String text,
  }) {
    return ContactTile(
      iconPath: AppAssets.whatsapp,
      text: text,
    );
  }

  static ContactTile phoneCall({
    required String text,
  }) {
    return ContactTile(
      iconPath: AppAssets.phone,
      text: text,
    );
  }

  static ContactTile website({
    required String text,
  }) {
    return ContactTile(
      iconPath: AppAssets.link,
      text: text,
    );
  }
}
