import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/ivent_create/controllers/ivent_create_state_manager.dart';
import 'package:ivent_app/routes/ivent_create.dart';
import 'package:ivent_app/shared/controllers/base_controller.dart';

class IventCreateImageController extends BaseController<IventCreateSharedState> {
  IventCreateImageController(AuthService authService, IventCreateSharedState state) : super(authService, state);

  final _suggestedImages = Rxn<GetSuggestedImagesReturn>();

  GetSuggestedImagesReturn? get suggestedImages => _suggestedImages.value;

  set suggestedImages(GetSuggestedImagesReturn? value) => _suggestedImages.value = value;

  Future<void> getSuggestedImagesPage() async {
    await runWithLoading(
      () async {
        suggestedImages = await authService.iventsApi.getSuggestedImages('');
        Get.toNamed(IventCreateRoutes.IVENT_OLUSTUR_IMAGE_SELECTION);
      },
      loadingTag: 'getSuggestedImages',
    );
  }
}
