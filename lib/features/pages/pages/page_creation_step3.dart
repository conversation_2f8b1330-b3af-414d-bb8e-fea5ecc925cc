import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/routes/page_creation.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/composite/buttons/hobby_buttons.dart';
import 'package:ivent_app/core/widgets/layout/scaffolds/ia_scaffold.dart';
import 'package:ivent_app/core/widgets/layout/screens/ia_search_screen.dart';
import 'package:ivent_app/features/pages/controllers/page_creation_controller.dart';
import 'package:ivent_app/features/pages/widgets/hobby_category_box.dart';
import 'package:ivent_app/shared/domain/entities/hobby.dart';
import 'package:ivent_app/api/api.dart';

class PageCreationStep3 extends StatefulWidget {
  const PageCreationStep3({Key? key}) : super(key: key);

  @override
  State<PageCreationStep3> createState() => _PageCreationStep3State();
}

class _PageCreationStep3State extends State<PageCreationStep3> {
  PageCreationController? _controller;
  TextEditingController? _searchBarController;

  @override
  void initState() {
    super.initState();
    _controller = Get.find<PageCreationController>();
    _searchBarController = TextEditingController();
  }

  @override
  void dispose() {
    _searchBarController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return IaScaffold.noSearch(
      title: 'Sayfa Oluştur',
      body: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Progress indicator
          Padding(
            padding: const EdgeInsets.all(AppDimensions.padding20),
            child: _buildProgressIndicator(),
          ),
          const SizedBox(height: AppDimensions.padding16),

            // Header
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
            child: _buildHeader(),
          ),
            const SizedBox(height: AppDimensions.padding16),

          // Search screen with selected hobbies and categories
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
              child: _buildSearchScreen(),
            ),
          ),

            // Navigation buttons
          Padding(
            padding: const EdgeInsets.all(AppDimensions.padding20),
            child: _buildNavigationButtons(),
          ),
          ],
      ),
    );
  }

  Widget _buildProgressIndicator() {
    return Row(
      children: [
        _buildProgressDot(0, true),
        _buildProgressLine(true),
        _buildProgressDot(1, true),
        _buildProgressLine(true),
        _buildProgressDot(2, true),
        _buildProgressLine(false),
        _buildProgressDot(3, false),
      ],
    );
  }

  Widget _buildProgressDot(int step, bool isActive) {
    return Container(
      width: 32,
      height: 32,
      decoration: BoxDecoration(
        color: isActive ? AppColors.primary : AppColors.lightGrey,
        shape: BoxShape.circle,
      ),
      child: Center(
        child: Text(
          '${step + 1}',
          style: AppTextStyles.size14Bold.copyWith(
            color: isActive ? AppColors.white : AppColors.textSecondary,
          ),
        ),
      ),
    );
  }

  Widget _buildProgressLine(bool isActive) {
    return Expanded(
      child: Container(
        height: 2,
        color: isActive ? AppColors.primary : AppColors.lightGrey,
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'İlgi Alanları',
              style: AppTextStyles.size20Bold,
            ),
            Text(
              ' *',
              style: AppTextStyles.size20Bold.copyWith(color: AppColors.error),
            ),
          ],
        ),
        const SizedBox(height: AppDimensions.padding8),
        Text(
          'Sayfanızla ilgili ilgi alanlarını seçin',
          style: AppTextStyles.size14Regular.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }

  Widget _buildSearchScreen() {
    final searchController = _searchBarController;
    if (searchController == null) return const SizedBox();
    
    return IaSearchScreen(
      textEditingController: searchController,
      body: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
          if (_selectedHobbyIds.isNotEmpty) _buildSelectedHobbies(),
          Expanded(child: _buildAvailableHobbies()),
          ],
        ),
      );
  }

  Widget _buildSelectedHobbies() {
    return Container(
      margin: const EdgeInsets.only(bottom: AppDimensions.padding16),
      height: 50,
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        shrinkWrap: true,
        itemCount: _selectedHobbyIds.length,
        itemBuilder: (context, index) => HobbyButtons.selectedHobbyTag(
          onTap: () => _handleHobbyToggle(_selectedHobbyIds[index]),
          text: Hobby.getHobbyNameFromHobbyId(_selectedHobbyIds[index]),
        ),
        separatorBuilder: (context, index) => const SizedBox(width: AppDimensions.padding8),
      ),
    );
  }

  Widget _buildAvailableHobbies() {
    final categories = _availableHobbies.keys.toList();
    final hobbyLists = _availableHobbies.values.toList();

    return ListView.separated(
      padding: const EdgeInsets.only(bottom: 100),
      itemCount: categories.length,
      itemBuilder: (context, index) {
        return HobbyCategoryBox(
          mainCategory: categories.elementAt(index),
          hobbyList: hobbyLists.elementAt(index),
          selectedHobbyIds: _selectedHobbyIds,
          onHobbyToggle: _handleHobbyToggle,
        );
      },
      separatorBuilder: (context, index) => const SizedBox(height: AppDimensions.padding16),
    );
  }

  List<String> get _selectedHobbyIds => _controller?.selectedHobbies.map((h) => h.hobbyId).toList() ?? [];

  Map<String, List<Hobby>> get _availableHobbies => {
        'Müzik': Hobby.hobbyListByParentHobbyName['Müzik']!,
        'Sanat & Kültür': Hobby.hobbyListByParentHobbyName['Sanat & Kültür']!,
        'Spor': Hobby.hobbyListByParentHobbyName['Spor']!,
        'Kariyer & Akademik': Hobby.hobbyListByParentHobbyName['Kariyer & Akademik']!,
        'Yeme İçme': Hobby.hobbyListByParentHobbyName['Yeme İçme']!,
        'Toplum': Hobby.hobbyListByParentHobbyName['Toplum']!,
      };

  void _handleHobbyToggle(String hobbyId) {
    // Find the hobby by ID and create HobbyItem
    final hobby = _findHobbyById(hobbyId);
    if (hobby != null) {
      final hobbyItem = HobbyItem(
        hobbyId: hobby.hobbyId,
        hobbyName: hobby.hobbyName,
        parentHobbyId: hobby.parentHobbyId ?? '',
        parentHobbyName: hobby.parentHobbyName ?? '',
      );
      _controller?.toggleHobbySelection(hobbyItem);
      setState(() {});
    }
  }

  Hobby? _findHobbyById(String hobbyId) {
    for (final hobbyList in _availableHobbies.values) {
      for (final hobby in hobbyList) {
        if (hobby.hobbyId == hobbyId) {
          return hobby;
        }
      }
    }
    return null;
  }

  Widget _buildNavigationButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: () => Get.back(),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: AppDimensions.padding16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              ),
              side: const BorderSide(color: AppColors.lightGrey),
            ),
            child: Text(
              'Geri',
              style: AppTextStyles.size16Medium.copyWith(color: AppColors.textSecondary),
            ),
          ),
        ),
        const SizedBox(width: AppDimensions.padding16),
        Expanded(
          flex: 2,
          child: Obx(() => ElevatedButton(
            onPressed: (_controller?.canProceedFromStep(2) ?? false)
                ? () => Get.toNamed(PageCreationRoutes.STEP4)
                : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              disabledBackgroundColor: AppColors.lightGrey,
              padding: const EdgeInsets.symmetric(vertical: AppDimensions.padding16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              ),
            ),
            child: Text(
              'Devam Et',
              style: AppTextStyles.size16Bold.copyWith(
                color: (_controller?.canProceedFromStep(2) ?? false)
                    ? AppColors.white 
                    : AppColors.textSecondary,
              ),
            ),
          )),
        ),
      ],
    );
  }
} 