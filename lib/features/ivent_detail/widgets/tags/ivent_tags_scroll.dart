import 'package:flutter/widgets.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/widgets/specialized/ivent/ivent_detail_buttons.dart';

/// A horizontal scrollable widget that displays a list of ivent tags.
///
/// This widget creates a horizontally scrollable list of tags associated with
/// an ivent. Each tag is displayed as a button that can be tapped to perform
/// actions (typically filtering or navigation).
///
/// The widget follows the project's design patterns and uses consistent spacing
/// and styling for tag display.
///
/// Usage:
/// ```dart
/// IventTagsScroll(
///   tags: ['Music', 'Outdoor', 'Social'],
///   margin: EdgeInsets.symmetric(vertical: 12.0),
/// )
/// ```
class IventTagsScroll extends StatelessWidget {
  /// List of tag strings to display
  final List<String> tags;
  
  /// Margin around the entire scroll widget
  final EdgeInsetsGeometry margin;
  
  /// Optional callback for when a tag is tapped
  final Function(String tag)? onTagTap;

  /// Creates a horizontal scrollable tags widget.
  ///
  /// The [tags] parameter is required and contains the list of tag strings
  /// to display. The [margin] parameter controls spacing around the widget
  /// and defaults to symmetric vertical padding.
  const IventTagsScroll({
    super.key,
    required this.tags,
    this.margin = const EdgeInsets.symmetric(vertical: 8.0),
    this.onTagTap,
  });

  @override
  Widget build(BuildContext context) {
    // Don't render if there are no tags
    if (tags.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      height: AppDimensions.buttonHeightIventTag,
      margin: margin,
      child: ListView.separated(
        padding: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
        scrollDirection: Axis.horizontal,
        itemCount: tags.length,
        itemBuilder: (BuildContext context, index) {
          final tag = tags[index];
          return IventDetailButtons.iventDetailTag(
            onTap: () => onTagTap?.call(tag),
            text: tag,
          );
        },
        separatorBuilder: (BuildContext context, int index) => 
            const SizedBox(width: AppDimensions.padding12),
      ),
    );
  }
}
