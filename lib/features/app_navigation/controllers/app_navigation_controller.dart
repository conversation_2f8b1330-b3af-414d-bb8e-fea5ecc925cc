import 'package:get/get.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/app_navigation/controllers/app_navigation_state_manager.dart';
import 'package:ivent_app/features/home/<USER>/home_controller.dart';
import 'package:ivent_app/features/home/<USER>/home_state_manager.dart';
import 'package:ivent_app/features/notifications/controllers/notification_controller.dart';
import 'package:ivent_app/features/notifications/controllers/notification_state_manager.dart';
import 'package:ivent_app/features/profile/controllers/profile_controller.dart';
import 'package:ivent_app/features/profile/controllers/profile_state_manager.dart';
import 'package:ivent_app/features/vibes/controllers/vibe_upload_controller.dart';
import 'package:ivent_app/features/vibes/controllers/vibes_page_controller.dart';
import 'package:ivent_app/features/vibes/controllers/vibes_state_manager.dart';
import 'package:ivent_app/shared/controllers/base_controller.dart';

class AppNavigationController extends BaseController {
  final HomeSharedState homeSharedState;
  final ProfileSharedState profileSharedState;

  AppNavigationController(
    AuthService authService,
    this.homeSharedState,
    this.profileSharedState,
  ) : super(authService, AppNavigationSharedState());

  @override
  void initController() async {
    super.initController();
    final userId = sessionUser.sessionId;

    Get.lazyPut(() => HomeController(authService, homeSharedState), fenix: true);
    Get.lazyPut(() => VibesPageController(authService, VibesSharedState('')), fenix: true);
    Get.lazyPut(() => VibeUploadController(authService, VibesSharedState('')), fenix: true);
    Get.lazyPut(() => NotificationController(authService, NotificationSharedState()), fenix: true);
    Get.lazyPut(() => ProfileController(authService, profileSharedState), tag: userId, fenix: true);
  }

  @override
  void closeController() {
    Get.delete<HomeController>();
    Get.delete<VibesPageController>();
    Get.delete<VibeUploadController>();
    Get.delete<NotificationController>();
    Get.delete<ProfileController>(tag: sessionUser.sessionId);
    super.closeController();
  }
}
