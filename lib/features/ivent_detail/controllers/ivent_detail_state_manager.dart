import 'package:get/get.dart';
import 'package:ivent_app/shared/controllers/shared_state.dart';

/// State manager for iVent detail feature
///
/// Manages shared reactive state across all iVent detail controllers.
/// This includes navigation state, UI state, and shared data that needs
/// to be accessible across multiple controllers within the feature.
class IventDetailSharedState extends SharedState {
  // Constants
  final String iventId;

  // Reactive state
  final _hasError = false.obs;
  final _errorMessage = ''.obs;

  // Constructor
  IventDetailSharedState(this.iventId);

  // Getters
  bool get hasError => _hasError.value;
  String get errorMessage => _errorMessage.value;

  // Setters
  set hasError(bool value) => _hasError.value = value;
  set errorMessage(String value) => _errorMessage.value = value;

  // Methods

  /// Clears any error state
  void clearError() {
    hasError = false;
    errorMessage = '';
  }

  /// Sets error state with message
  void setError(String message) {
    hasError = true;
    errorMessage = message;
  }
}
