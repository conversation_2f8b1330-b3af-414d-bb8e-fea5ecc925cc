import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/pages/controllers/page_creation_state_manager.dart';
import 'package:ivent_app/shared/controllers/base_controller.dart';

/// Controller for page creation flow
///
/// Manages a 3-step page creation process:
/// 1. Page name and thumbnail selection
/// 2. Description, links, location, edu/membership settings
/// 3. Hobby tags and admin selection
class PageCreationController extends BaseController<PageCreationSharedState> {
  PageCreationController(AuthService authService, PageCreationSharedState state) : super(authService, state);

  // Step management
  final _currentStep = 0.obs;

  // Step 1: Basic Info
  final pageNameController = TextEditingController();
  final _selectedThumbnail = Rx<File?>(null);
  final _thumbnailUrl = ''.obs;

  // Step 2: Details
  final descriptionController = TextEditingController();
  final websiteUrlController = TextEditingController();
  final _selectedLocation = Rx<LocationItem?>(null);
  final _isEdu = false.obs;
  final _haveMembership = false.obs;

  // Step 3: Tags and Admins
  final _selectedHobbies = <HobbyItem>[].obs;
  final _selectedAdmins = <UserListItem>[].obs;

  // Search states
  final _locations = <LocationItem>[].obs;
  final _hobbies = <HobbyItem>[].obs;
  final _users = <UserListItem>[].obs;
  final _isSearchingLocations = false.obs;
  final _isSearchingHobbies = false.obs;
  final _isSearchingUsers = false.obs;

  // Step 2: Location Search - Using Mapbox API like ivent create
  final _searchSuggestions = <SearchBoxSuggestFeature>[].obs;

  List<SearchBoxSuggestFeature> get searchSuggestions => _searchSuggestions;

  // Getters
  int get currentStep => _currentStep.value;
  File? get selectedThumbnail => _selectedThumbnail.value;
  String get thumbnailUrl => _thumbnailUrl.value;
  LocationItem? get selectedLocation => _selectedLocation.value;
  bool get isEdu => _isEdu.value;
  bool get haveMembership => _haveMembership.value;
  List<HobbyItem> get selectedHobbies => _selectedHobbies;
  List<UserListItem> get selectedAdmins => _selectedAdmins;
  List<LocationItem> get locations => _locations;
  List<HobbyItem> get hobbies => _hobbies;
  List<UserListItem> get users => _users;
  bool get isSearchingLocations => _isSearchingLocations.value;
  bool get isSearchingHobbies => _isSearchingHobbies.value;
  bool get isSearchingUsers => _isSearchingUsers.value;

  // Setters
  set currentStep(int value) => _currentStep.value = value;
  set selectedThumbnail(File? value) => _selectedThumbnail.value = value;
  set thumbnailUrl(String value) => _thumbnailUrl.value = value;
  set selectedLocation(LocationItem? value) => _selectedLocation.value = value;
  set isEdu(bool value) => _isEdu.value = value;
  set haveMembership(bool value) => _haveMembership.value = value;

  @override
  void initController() {
    super.initController();
    debugPrint('PageCreationController initialized for user: ${sessionUser.sessionId}');
  }

  @override
  void closeController() {
    pageNameController.dispose();
    descriptionController.dispose();
    websiteUrlController.dispose();
    super.closeController();
  }

  // Step Navigation
  void nextStep() {
    if (currentStep < 3) {
      // Now 4 steps (0-3)
      currentStep++;
    }
  }

  void previousStep() {
    if (currentStep > 0) {
      currentStep--;
    }
  }

  bool canProceedFromStep(int step) {
    switch (step) {
      case 0:
        return pageNameController.text.trim().isNotEmpty;
      case 1:
        return selectedLocation != null;
      case 2:
        return selectedHobbies.isNotEmpty; // At least one hobby selected
      case 3:
        return true; // Final step - admin selection is optional
      default:
        return false;
    }
  }

  // Step 1: Image Selection
  Future<void> selectThumbnailFromGallery() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 85,
      );

      if (image != null) {
        selectedThumbnail = File(image.path);
        await _convertImageToBase64();
        debugPrint('✅ [DEBUG] Thumbnail selected: ${image.path}');
      }
    } catch (e) {
      debugPrint('❌ [DEBUG] Error selecting thumbnail: $e');
      Get.snackbar(
        'Hata',
        'Fotoğraf seçilirken bir hata oluştu.',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  Future<void> _convertImageToBase64() async {
    if (selectedThumbnail == null) return;

    try {
      final bytes = await selectedThumbnail!.readAsBytes();
      final base64String = base64Encode(bytes);

      // Convert to data URL format for API
      thumbnailUrl = 'data:image/jpeg;base64,$base64String';

      debugPrint('✅ [DEBUG] Image converted to base64 (${base64String.length} chars)');
      debugPrint('✅ [DEBUG] Thumbnail URL ready for API: ${thumbnailUrl.substring(0, 50)}...');
    } catch (e) {
      debugPrint('❌ [DEBUG] Error converting image: $e');
    }
  }

  // Step 2: Location Search - Using Mapbox API like ivent create
  Future<void> searchLocations(String query) async {
    if (query.trim().isEmpty) {
      _searchSuggestions.clear();
      return;
    }

    try {
      _isSearchingLocations.value = true;
      debugPrint('🔍 [DEBUG] Searching locations with Mapbox: $query');

      final result = await authService.mapboxApi.searchBoxSuggest(
        query,
        sessionUser.sessionId,
        limit: 10,
        types: 'country,region,postcode,district,place,city,locality,neighborhood,street,address,poi,category',
      );

      if (result != null) {
        _searchSuggestions.assignAll(result.suggestions);
        debugPrint('✅ [DEBUG] Found ${result.suggestions.length} location suggestions');
      } else {
        _searchSuggestions.clear();
        debugPrint('⚠️ [DEBUG] No location suggestions found');
      }
    } catch (e) {
      debugPrint('❌ [DEBUG] Error searching locations: $e');
      _searchSuggestions.clear();
    } finally {
      _isSearchingLocations.value = false;
    }
  }

  Future<void> selectLocationFromSuggestion(SearchBoxSuggestFeature suggestion) async {
    try {
      _isSearchingLocations.value = true;
      debugPrint('📍 [DEBUG] Selecting location: ${suggestion.name}');

      final result = await authService.mapboxApi.searchBoxRetrieve(
        suggestion.mapboxId,
        sessionUser.sessionId,
      );

      if (result != null && result.features.isNotEmpty) {
        final feature = result.features[0];

        // Convert Mapbox feature to LocationItem for compatibility
        final locationItem = LocationItem(
          locationId: suggestion.mapboxId, // Use mapboxId as locationId
          mapboxId: suggestion.mapboxId,
          locationName: suggestion.name,
          openAddress: suggestion.fullAddress ?? suggestion.placeFormatted,
          latitude: feature.properties.coordinates.latitude,
          longitude: feature.properties.coordinates.longitude,
          state: suggestion.context.region?.name ?? '',
        );

        selectedLocation = locationItem;
        _searchSuggestions.clear();
        debugPrint('✅ [DEBUG] Location selected: ${locationItem.locationName}');
      }
    } catch (e) {
      debugPrint('❌ [DEBUG] Error selecting location: $e');
    } finally {
      _isSearchingLocations.value = false;
    }
  }

  Future<void> loadLatestLocations() async {
    // Keep the existing implementation as fallback
    try {
      _isSearchingLocations.value = true;
      debugPrint('📍 [DEBUG] Loading latest locations');

      final result = await authService.locationsApi.getLatestLocations(
        limit: 10,
        page: 1,
      );

      if (result != null) {
        _locations.assignAll(result.locations);
        debugPrint('✅ [DEBUG] Loaded ${result.locations.length} latest locations');
      }
    } catch (e) {
      debugPrint('❌ [DEBUG] Error loading latest locations: $e');
    } finally {
      _isSearchingLocations.value = false;
    }
  }

  // Step 3: Hobby Selection - Load all hobbies from DB
  Future<void> loadAllHobbies() async {
    try {
      _isSearchingHobbies.value = true;
      debugPrint('📚 [DEBUG] Loading all hobbies from database');

      final result = await authService.hobbiesApi.searchHobbies(
        HobbiesSearchOriginEnum.default_,
        q: null, // No query to get all hobbies
        limit: 100, // High limit to get all hobbies
        page: 1,
      );

      if (result != null) {
        _hobbies.assignAll(result.hobbies);
        debugPrint('✅ [DEBUG] Loaded ${result.hobbies.length} hobbies from database');
      } else {
        _hobbies.clear();
        debugPrint('⚠️ [DEBUG] No hobbies found in database');
      }
    } catch (e) {
      debugPrint('❌ [DEBUG] Error loading hobbies: $e');
      _hobbies.clear();
    } finally {
      _isSearchingHobbies.value = false;
    }
  }

  // Step 3: Hobby Search (keep for search functionality)
  Future<void> searchHobbies(String query) async {
    try {
      _isSearchingHobbies.value = true;
      debugPrint('🔍 [DEBUG] Searching hobbies: $query');

      final result = await authService.hobbiesApi.searchHobbies(
        HobbiesSearchOriginEnum.default_,
        q: query.isEmpty ? null : query,
        limit: 20,
        page: 1,
      );

      if (result != null) {
        _hobbies.assignAll(result.hobbies);
        debugPrint('✅ [DEBUG] Found ${result.hobbies.length} hobbies');
      } else {
        _hobbies.clear();
        debugPrint('⚠️ [DEBUG] No hobbies found');
      }
    } catch (e) {
      debugPrint('❌ [DEBUG] Error searching hobbies: $e');
      _hobbies.clear();
    } finally {
      _isSearchingHobbies.value = false;
    }
  }

  void toggleHobbySelection(HobbyItem hobby) {
    if (isHobbySelected(hobby)) {
      _selectedHobbies.removeWhere((h) => h.hobbyId == hobby.hobbyId);
      debugPrint('➖ [DEBUG] Removed hobby: ${hobby.hobbyName}');
    } else {
      _selectedHobbies.add(hobby);
      debugPrint('➕ [DEBUG] Added hobby: ${hobby.hobbyName}');
    }
  }

  bool isHobbySelected(HobbyItem hobby) {
    return _selectedHobbies.any((h) => h.hobbyId == hobby.hobbyId);
  }

  // Admin Search
  Future<void> searchModeratorsForPageCreation(String query) async {
    try {
      _isSearchingUsers.value = true;
      debugPrint('🔍 [DEBUG] Searching moderators: $query');

      final result = await authService.pageMembershipsApi.searchModeratorsForPageCreation(
        q: query.isEmpty ? null : query,
        limit: 20,
        page: 1,
      );

      if (result != null) {
        _users.assignAll(result.users);
        debugPrint('✅ [DEBUG] Found ${result.users.length} potential moderators');
      } else {
        _users.clear();
        debugPrint('⚠️ [DEBUG] No moderators found');
      }
    } catch (e) {
      debugPrint('❌ [DEBUG] Error searching moderators: $e');
      _users.clear();
    } finally {
      _isSearchingUsers.value = false;
    }
  }

  void toggleAdminSelection(UserListItem user) {
    if (isAdminSelected(user)) {
      _selectedAdmins.removeWhere((u) => u.userId == user.userId);
      debugPrint('➖ [DEBUG] Removed admin: ${user.username}');
    } else {
      _selectedAdmins.add(user);
      debugPrint('➕ [DEBUG] Added admin: ${user.username}');
    }
  }

  bool isAdminSelected(UserListItem user) {
    return _selectedAdmins.any((u) => u.userId == user.userId);
  }

  // Page Creation
  Future<void> createPage() async {
    if (!_validateForm()) return;

    try {
      debugPrint('🚀 [DEBUG] Creating page...');

      final createPageDto = CreatePageDto(
        pageName: pageNameController.text.trim(),
        thumbnailUrl: thumbnailUrl.isNotEmpty ? thumbnailUrl : null,
        websiteUrl: websiteUrlController.text.trim().isNotEmpty ? websiteUrlController.text.trim() : null,
        description: descriptionController.text.trim().isNotEmpty ? descriptionController.text.trim() : null,
        isEdu: isEdu,
        haveMembership: haveMembership,
        tagIds: selectedHobbies.map((h) => h.hobbyId).toList(),
        creatorIds: selectedAdmins.map((u) => u.userId).toList(),
        locationId: selectedLocation!.locationId,
      );

      debugPrint('📋 [DEBUG] Page creation data:');
      debugPrint('  - Name: ${createPageDto.pageName}');
      debugPrint('  - Location: ${selectedLocation!.locationName}');
      debugPrint('  - IsEdu: ${createPageDto.isEdu}');
      debugPrint('  - HaveMembership: ${createPageDto.haveMembership}');
      debugPrint('  - Tags: ${createPageDto.tagIds.length}');
      debugPrint('  - Admins: ${createPageDto.creatorIds.length}');

      final result = await authService.pagesApi.createPage(createPageDto);

      if (result != null) {
        debugPrint('✅ [DEBUG] Page created successfully: ${result.pageId}');

        Get.snackbar(
          'Başarılı',
          'Sayfa başarıyla oluşturuldu!',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );

        // Navigate back to previous page
        Get.back();
        Get.back();
        Get.back(); // Go back 3 steps to return to main page
      }
    } catch (e) {
      debugPrint('❌ [DEBUG] Page creation error: $e');

      String errorMessage = 'Sayfa oluşturulurken bir hata oluştu.';

      if (e is ApiException) {
        debugPrint('❌ [DEBUG] API Exception details:');
        debugPrint('  - Status Code: ${e.code}');
        debugPrint('  - Message: ${e.message}');

        if (e.code == 400) {
          if (e.message?.contains('pageName') == true) {
            errorMessage = 'Sayfa adı zaten kullanımda veya geçersiz.';
          } else if (e.message?.contains('location') == true) {
            errorMessage = 'Konum bilgisi geçersiz.';
          }
        }
      }

      Get.snackbar(
        'Hata',
        errorMessage,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {}
  }

  bool _validateForm() {
    if (pageNameController.text.trim().isEmpty) {
      Get.snackbar('Hata', 'Sayfa adı gereklidir.');
      currentStep = 0;
      return false;
    }

    if (selectedLocation == null) {
      Get.snackbar('Hata', 'Konum seçimi gereklidir.');
      currentStep = 1;
      return false;
    }

    return true;
  }

  // Form Reset
  void resetForm() {
    currentStep = 0;
    pageNameController.clear();
    descriptionController.clear();
    websiteUrlController.clear();
    selectedThumbnail = null;
    thumbnailUrl = '';
    selectedLocation = null;
    isEdu = false;
    haveMembership = false;
    _selectedHobbies.clear();
    _selectedAdmins.clear();
    _locations.clear();
    _hobbies.clear();
    _users.clear();
  }
}
