import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/layout/scaffolds/ia_scaffold.dart';
import 'package:ivent_app/features/profile/controllers/profile_state_manager.dart';
import 'package:ivent_app/features/profile/controllers/sub_controllers/profile_level_steps_controller.dart';

class ProfileLevelStepsPage extends StatefulWidget {
  const ProfileLevelStepsPage({super.key});

  @override
  State<ProfileLevelStepsPage> createState() => _ProfileLevelStepsPageState();
}

class _ProfileLevelStepsPageState extends State<ProfileLevelStepsPage> {
  late final ProfileLevelStepsController _controller;

  @override
  void initState() {
    super.initState();
    _controller = Get.put(ProfileLevelStepsController(Get.find(), Get.find<ProfileSharedState>()));
  }

  @override
  void dispose() {
    Get.delete<ProfileLevelStepsController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return IaScaffold.noSearch(
      title: 'Bilinçli Hesap',
      body: Obx(() {
        if (_controller.isLoading()) {
          return const Center(child: CircularProgressIndicator());
        }

        if (_controller.hasError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  _controller.errorMessage,
                  style: AppTextStyles.size16Regular.copyWith(
                    color: AppColors.error,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: AppDimensions.padding16),
                GestureDetector(
                  onTap: () => _controller.initController(),
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppDimensions.padding16,
                      vertical: AppDimensions.padding8,
                    ),
                    decoration: BoxDecoration(
                      color: AppColors.primary,
                      borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                    ),
                    child: Text(
                      'Tekrar Dene',
                      style: AppTextStyles.size14Bold.copyWith(
                        color: AppColors.white,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(AppDimensions.padding16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              _buildHeader(),
              const SizedBox(height: AppDimensions.padding24),

              // Steps List
              _buildStepsList(),

              const SizedBox(height: AppDimensions.padding24),

              // Action Button
              _buildActionButton(),
            ],
          ),
        );
      }),
    );
  }

  Widget _buildHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          _controller.levelTitle,
          style: AppTextStyles.size20Bold,
        ),
        const SizedBox(height: AppDimensions.padding8),
        Text(
          _controller.levelDescription,
          style: AppTextStyles.size14Regular.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }

  Widget _buildStepsList() {
    return ListView.separated(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: _controller.steps.length,
      separatorBuilder: (context, index) => const SizedBox(height: AppDimensions.padding12),
      itemBuilder: (context, index) {
        final step = _controller.steps[index];
        return _buildStepItem(step, index);
      },
    );
  }

  Widget _buildStepItem(LevelStep step, int index) {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.padding16),
      decoration: BoxDecoration(
        color: step.isCompleted ? AppColors.primary.withOpacity(0.1) : AppColors.grey50,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: step.isCompleted ? AppColors.primary : AppColors.grey200,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // Step Number or Check Icon
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: step.isCompleted ? AppColors.primary : AppColors.grey300,
              shape: BoxShape.circle,
            ),
            child: Center(
              child: step.isCompleted
                  ? const Icon(
                      Icons.check,
                      color: AppColors.white,
                      size: 20,
                    )
                  : Text(
                      '${index + 1}',
                      style: AppTextStyles.size14Bold.copyWith(
                        color: AppColors.white,
                      ),
                    ),
            ),
          ),
          const SizedBox(width: AppDimensions.padding12),

          // Step Content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  step.title,
                  style: AppTextStyles.size16Bold.copyWith(
                    color: step.isCompleted ? AppColors.primary : AppColors.textPrimary,
                  ),
                ),
                if (step.description.isNotEmpty) ...[
                  const SizedBox(height: AppDimensions.padding4),
                  Text(
                    step.description,
                    style: AppTextStyles.size14Regular.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ],
            ),
          ),

          // Action Button
          if (step.isActionable && !step.isCompleted)
            GestureDetector(
              onTap: () => _controller.onStepAction(step),
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppDimensions.padding12,
                  vertical: AppDimensions.padding8,
                ),
                decoration: BoxDecoration(
                  color: AppColors.primary,
                  borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                ),
                child: Text(
                  step.actionText,
                  style: AppTextStyles.size12Bold.copyWith(
                    color: AppColors.white,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildActionButton() {
    if (_controller.isAllStepsCompleted) {
      return Container(
        width: double.infinity,
        padding: const EdgeInsets.all(AppDimensions.padding16),
        decoration: BoxDecoration(
          color: AppColors.primary,
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.check_circle,
              color: AppColors.white,
              size: 24,
            ),
            const SizedBox(width: AppDimensions.padding8),
            Text(
              'Tebrikler! Tüm adımlar tamamlandı',
              style: AppTextStyles.size16Bold.copyWith(
                color: AppColors.white,
              ),
            ),
          ],
        ),
      );
    }

    return GestureDetector(
      onTap: _controller.onNextStep,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(AppDimensions.padding16),
        decoration: BoxDecoration(
          color: _controller.canProceedToNextStep ? AppColors.primary : AppColors.grey300,
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        ),
        child: Center(
          child: Text(
            _controller.nextStepButtonText,
            style: AppTextStyles.size16Bold.copyWith(
              color: AppColors.white,
            ),
          ),
        ),
      ),
    );
  }
}
