import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/specialized/ivent/ivent_create_buttons.dart';
import 'package:ivent_app/features/ivent_create/controllers/ivent_create_controller.dart';
import 'package:ivent_app/features/ivent_create/widgets/forms/ivent_create_form_list_tile.dart';

class IventCreateRegisterTypePanel extends StatefulWidget {
  const IventCreateRegisterTypePanel({super.key});

  @override
  State<IventCreateRegisterTypePanel> createState() => _IventCreateRegisterTypePanelState();
}

class _IventCreateRegisterTypePanelState extends State<IventCreateRegisterTypePanel> {
  final IventCreateController _controller = Get.find();

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 650 - 28,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: AppDimensions.padding8),
            Text('Kayıt Türü Seçiniz', style: AppTextStyles.size16Bold),
            Obx(() {
              return Text(
                _controller.formController.isThereAnyRegisterType
                    ? 'Etkinliğinize kayıt toplamak için en fazla 3 adet tane kayıt türü seçiniz.'
                    : 'Etkinliğinize nasıl kayıt toplayacaksınız',
                style: AppTextStyles.size14MediumTextSecondary,
              );
            }),
            IventCreateFormListTile.googleFormsUrlTile(
                onChanged: (val) => _controller.formController.googleFormsUrl = val),
            IventCreateFormListTile.instagramUsernameTile(
                onChanged: (val) => _controller.formController.instagramUsername = val),
            IventCreateFormListTile.whatsappUrlTile(onChanged: (val) => _controller.formController.whatsappUrl = val),
            IventCreateFormListTile.whatsappNumberTile(
                onChanged: (val) => _controller.formController.whatsappNumber = val),
            IventCreateFormListTile.callNumberTile(onChanged: (val) => _controller.formController.callNumber = val),
            IventCreateFormListTile.websiteUrlTile(onChanged: (val) => _controller.formController.websiteUrl = val),
            const Spacer(),
            Obx(() {
              return IventCreateButtons.iventCreatePublishWithoutRegister(
                onTap: () => _controller.formController.submitIvent(context),
                isEnabled: !_controller.formController.isThereAnyRegisterType,
              );
            }),
            const SizedBox(height: AppDimensions.padding12),
            Obx(() {
              return IventCreateButtons.iventCreatePublish(
                onTap: () => _controller.formController.submitIvent(context),
                isEnabled: _controller.formController.isThereAnyRegisterType,
              );
            }),
            const SizedBox(height: AppDimensions.padding20),
          ],
        ),
      ),
    );
  }
}
